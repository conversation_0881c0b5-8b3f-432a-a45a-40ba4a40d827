#!/usr/bin/env python3
"""
Instagram Scraping Test Script
Tests various Instagram URLs to identify the blank page issue
"""

import asyncio
import sys
import os
import tempfile
import time
from playwright.async_api import async_playwright

# Add the app directory to the Python path
sys.path.append('/home/<USER>/WebReview_DS_API_24Jun/app')

from services.screenshot.playwright_driver import (
    capture_screenshot,
    handle_instagram_login_redirect,
    normalize_instagram_url
)

# Test URLs
TEST_URLS = [
    "https://www.instagram.com/",
    "https://www.instagram.com/supertails.official",
    "https://www.instagram.com/nike",
    "https://www.instagram.com/cocacola",
    "https://www.instagram.com/natgeo"
]

async def test_instagram_url(url: str, test_name: str):
    """Test a single Instagram URL"""
    print(f"\n{'='*60}")
    print(f"Testing: {test_name}")
    print(f"URL: {url}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # Normalize URL
        normalized_url = normalize_instagram_url(url)
        print(f"Normalized URL: {normalized_url}")
        
        # Capture screenshot using Playwright
        async with async_playwright() as p:
            screenshot_bytes = await capture_screenshot(
                playwright=p,
                url=normalized_url,
                close_popups=True,  # Instagram is social media
                image_load_wait=3000
            )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if screenshot_bytes:
            # Save screenshot to temp file for inspection
            with tempfile.NamedTemporaryFile(delete=False, suffix=".png", prefix=f"instagram_test_{test_name}_") as temp_file:
                temp_file.write(screenshot_bytes)
                temp_path = temp_file.name
            
            print(f"✅ SUCCESS: Screenshot captured ({len(screenshot_bytes)} bytes)")
            print(f"⏱️  Duration: {duration:.2f} seconds")
            print(f"📁 Saved to: {temp_path}")
            
            # Check if screenshot is likely blank (very small file size)
            if len(screenshot_bytes) < 5000:  # Less than 5KB is likely blank
                print(f"⚠️  WARNING: Screenshot is very small ({len(screenshot_bytes)} bytes) - likely blank!")
            
        else:
            print(f"❌ FAILED: No screenshot captured")
            print(f"⏱️  Duration: {duration:.2f} seconds")
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ ERROR: {str(e)}")
        print(f"⏱️  Duration: {duration:.2f} seconds")

async def test_direct_playwright_access():
    """Test direct Playwright access to Instagram"""
    print(f"\n{'='*60}")
    print("Testing Direct Playwright Access to Instagram")
    print(f"{'='*60}")
    
    async with async_playwright() as p:
        # Test with different browsers
        browsers = [
            ("Chromium", p.chromium),
            ("Firefox", p.firefox),
        ]
        
        for browser_name, browser_type in browsers:
            print(f"\n--- Testing with {browser_name} ---")
            
            try:
                browser = await browser_type.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    ]
                )
                
                page = await browser.new_page()
                
                # Set viewport
                await page.set_viewport_size({"width": 1920, "height": 1080})
                
                # Navigate to Instagram
                url = "https://www.instagram.com/"
                print(f"Navigating to: {url}")
                
                response = await page.goto(url, timeout=30000)
                print(f"Response status: {response.status}")
                
                # Wait a bit for page to load
                await page.wait_for_timeout(3000)
                
                # Check current URL
                current_url = page.url
                print(f"Current URL: {current_url}")
                
                # Check page title
                title = await page.title()
                print(f"Page title: {title}")
                
                # Check if we're on login page
                if "/accounts/login" in current_url:
                    print("🔐 Redirected to login page")
                    
                    # Try to find login form elements
                    username_input = await page.query_selector('input[name="username"]')
                    password_input = await page.query_selector('input[name="password"]')
                    
                    if username_input and password_input:
                        print("✅ Login form elements found")
                    else:
                        print("❌ Login form elements not found")
                
                # Check page content
                body_text = await page.text_content('body')
                if body_text:
                    print(f"Body text length: {len(body_text)} characters")
                    if len(body_text.strip()) < 100:
                        print(f"⚠️  Very little content: '{body_text[:200]}...'")
                else:
                    print("❌ No body text found")
                
                # Take screenshot
                screenshot_bytes = await page.screenshot(full_page=True, type="png")
                
                with tempfile.NamedTemporaryFile(delete=False, suffix=".png", prefix=f"direct_{browser_name.lower()}_") as temp_file:
                    temp_file.write(screenshot_bytes)
                    temp_path = temp_file.name
                
                print(f"📁 Screenshot saved: {temp_path} ({len(screenshot_bytes)} bytes)")
                
                await browser.close()
                
            except Exception as e:
                print(f"❌ Error with {browser_name}: {str(e)}")
                if 'browser' in locals():
                    await browser.close()

async def main():
    """Main test function"""
    print("🔍 Instagram Scraping Investigation")
    print("=" * 60)
    
    # Test 1: Direct Playwright access
    await test_direct_playwright_access()
    
    # Test 2: Using our screenshot service
    print(f"\n{'='*60}")
    print("Testing with Screenshot Service")
    print(f"{'='*60}")
    
    for i, url in enumerate(TEST_URLS):
        test_name = f"test_{i+1}"
        await test_instagram_url(url, test_name)
        
        # Small delay between tests
        await asyncio.sleep(2)
    
    print(f"\n{'='*60}")
    print("🏁 Testing Complete")
    print(f"{'='*60}")
    print("Check the generated screenshot files to see what's being captured.")
    print("Look for files starting with 'instagram_test_' and 'direct_' in /tmp/")

if __name__ == "__main__":
    asyncio.run(main())

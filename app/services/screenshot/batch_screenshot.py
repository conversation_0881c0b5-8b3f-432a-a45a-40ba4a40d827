import asyncio
import time
import os
import tempfile
import traceback
from typing import List, Dict, Optional
from playwright.async_api import async_playwright
from app.services.screenshot.playwright_driver import capture_screenshot as capture_screenshot_with_playwright
from app.services.screenshot.blob_utils import upload_to_azure_container
from app.utils.logger import ConsoleLogger

# Constants
CONCURRENT_REQUESTS = 5  # Number of parallel screenshot jobs

class BatchScreenshotProcessor:
    def __init__(self, ref_id: str, analysis_type: str = "batch", max_concurrent: int = CONCURRENT_REQUESTS):
        self.ref_id = ref_id
        self.analysis_type = analysis_type
        self.logger = ConsoleLogger(ref_id or "batch_screenshot")
        self.max_concurrent = max_concurrent

    def _get_image_load_wait(self, url: str) -> int:
        """Calculate image loading wait time based on URL type"""
        # Platform-specific image loading wait times (in milliseconds)
        social_media_platforms = {
            "instagram.com": 5000,    # Instagram needs more time for image loading
            "facebook.com": 4000,     # Facebook has complex image loading
            "fb.com": 4000,          # Facebook short domain
            "twitter.com": 3500,     # Twitter/X has dynamic image loading
            "x.com": 3500,           # Twitter rebrand
            "linkedin.com": 4000,    # LinkedIn has professional images
            "youtube.com": 4500,     # YouTube thumbnails and video previews
            "youtu.be": 4500,        # YouTube short domain
            "pinterest.com": 5000,   # Pinterest is image-heavy
            "pin.it": 5000,          # Pinterest short domain
            "tiktok.com": 4000,      # TikTok video thumbnails
        }

        # Check if URL matches any social media platform
        url_lower = url.lower()
        image_load_wait = 3000  # Default 3 seconds for regular websites

        for platform, wait_time in social_media_platforms.items():
            if platform in url_lower:
                image_load_wait = wait_time
                break

        return image_load_wait

    async def _capture_and_upload(self, playwright, url: str, close_popups: bool) -> Optional[Dict[str, any]]:
        try:
            # Calculate image loading wait time for this URL
            image_load_wait = self._get_image_load_wait(url)

            screenshot_bytes = await capture_screenshot_with_playwright(
                playwright,
                url,
                close_popups=close_popups,
                image_load_wait=image_load_wait
            )
            if not screenshot_bytes:
                self.logger.warning(f"Failed to capture screenshot for {url}")
                return {"url": url, "success": False, "azure_url": "screenshot_failed"}

            # Use a temporary file to upload to Azure
            with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
                temp_file.write(screenshot_bytes)
                temp_path = temp_file.name

            # Generate filename for Azure upload
            import uuid
            file_name = f"{self.analysis_type}_{self.ref_id}_{uuid.uuid4()}.png"

            try:
                azure_url = await upload_to_azure_container(temp_path, file_name, self.logger)
                if not azure_url:
                    self.logger.error(f"Failed to upload screenshot to Azure for {url}")
                    return {"url": url, "success": False, "azure_url": "upload_failed"}

                return {"url": url, "success": True, "azure_url": azure_url}
            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_path)
                except Exception as cleanup_error:
                    self.logger.warning(f"Failed to clean up temp file: {cleanup_error}")

        except Exception as e:
            self.logger.error(f"Error in capture_and_upload for {url}: {e}", {"trace": traceback.format_exc()})
            return {"url": url, "success": False, "azure_url": "processing_error"}

    async def run(self, urls: List[str], popup_settings: Dict[str, bool]) -> Dict[str, Dict[str, any]]:
        start_time = time.time()
        results = {}
        
        async with async_playwright() as playwright:
            semaphore = asyncio.Semaphore(self.max_concurrent)
            tasks = []

            for url in urls:
                async def task_wrapper(target_url):
                    async with semaphore:
                        close_popups = popup_settings.get(target_url, False)
                        result = await self._capture_and_upload(playwright, target_url, close_popups)
                        results[target_url] = result
                tasks.append(task_wrapper(url))

            await asyncio.gather(*tasks)

        end_time = time.time()
        self.logger.info(f"Batch screenshot processing finished in {end_time - start_time:.2f}s for {len(urls)} URLs.")
        return results

async def capture_screenshots_for_analysis(urls: List[str], ref_id: str, analysis_type: str, 
                                           popup_settings: Dict[str, bool]) -> Dict[str, str]:
    if not urls:
        return {}
    
    processor = BatchScreenshotProcessor(ref_id, analysis_type)
    batch_results = await processor.run(urls, popup_settings)
    
    # Extract the URL to Azure URL mapping
    azure_urls = {
        result['url']: result['azure_url']
        for result in batch_results.values()
        if result.get('success') and result.get('azure_url')
    }
    return azure_urls 
# WebReview_DS_API_24Jun Information

## Summary
A FastAPI-based service for URL classification and website analysis. The system processes URLs from various sources and classifies them into categories using GPT models (OpenAI or Gemini). It includes features for policy analysis, MCC (Merchant Category Code) analysis, risky classification, and social media analysis.

## Structure
- **app/**: Main application code
  - **routers/**: API endpoints for different analysis types
  - **services/**: Business logic implementation
  - **models/**: Data models (DB and request/response)
  - **tasks/**: Celery task definitions
  - **utils/**: Utility functions
  - **gpt_models/**: GPT integration code
- **tests/**: Test files for the application
- **special/**: JSON files for specific websites
- **input/**: Input data files for analysis

## Language & Runtime
**Language**: Python
**Version**: 3.x (based on dependencies)
**Framework**: FastAPI (0.115.7)
**Task Queue**: Celery (5.4.0)

## Dependencies
**Main Dependencies**:
- fastapi (0.115.7): Web framework
- celery (5.4.0): Distributed task queue
- sqlmodel (0.0.22): SQL database ORM
- openai (1.60.0): OpenAI API client
- playwright (1.49.1): Browser automation
- mysql-connector-python (9.2.0): MySQL database connector
- redis (5.2.1): Redis client for Celery backend
- pydantic (2.10.5): Data validation

**Development Dependencies**:
- pytest: Testing framework
- pre_commit (4.1.0): Pre-commit hooks

## Database
**Type**: MySQL (primary) with SQLite support
**ORM**: SQLModel/SQLAlchemy
**Models**: Defined in app/models/db_models.py
**Connection**: Configured with connection pooling and timeout settings

## Build & Installation
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Initialize database
python init_mysql_database.py

# Run the application
uvicorn app.main:app --reload
```

## Celery Configuration
**Broker**: Redis (from environment variable CELERY_BROKER_URL)
**Backend**: Redis (from environment variable CELERY_RESULT_BACKEND)
**Tasks**:
- MCC analysis tasks
- Risky classification tasks
- Social media analysis tasks
- Policy analysis tasks

**Task Configuration**:
- Timeouts: 3-45 minutes depending on task type
- Retry mechanisms: Exponential backoff with jitter
- Rate limiting: 2-4 tasks per minute

## API Endpoints
**Main Routes**:
- `/risky-classification`: Risky classification endpoints
- `/health`: Health check endpoint

**Commented Out Routes**:
- `/policy-analysis`: Policy analysis endpoints (currently disabled)
- `/mcc-analysis`: MCC analysis endpoints (currently disabled)

## Testing
**Framework**: pytest
**Test Files**: Located in /tests directory
**Test Types**:
- API integration tests
- Service unit tests
- Mock-based tests for external dependencies

**Run Command**:
```bash
pytest tests/
```

## Environment Configuration
**Config File**: app/config.py
**Environment Variables**:
- DATABASE_URL: Database connection string
- OPENAI_API_KEY: OpenAI API key
- GEMINI_API_KEY: Google Gemini API key
- CELERY_BROKER_URL: Celery broker URL
- CELERY_RESULT_BACKEND: Celery result backend URL
- Various Azure configuration variables

## Features
- URL classification into predefined categories
- Policy analysis with enhanced features
- MCC (Merchant Category Code) analysis
- Risky website classification
- Social media analysis
- Background task processing with Celery
- Comprehensive logging
- Database storage of analysis results

## Token Management
**Automatic Token Limiting**:
- Implements token limiting to ensure GPT API calls stay within 90K token limits
- Estimates token usage for each URL and tracks cumulative token count
- Dynamically limits the number of URLs sent to GPT models
- Preserves original URL indices for accurate mapping of results
- Implements emergency URL trimming when approaching token limits
- Specialized token limit handling for different analysis types (MCC, policy, etc.)
- Text cropping functions to fit within model context windows
"""
Risky Classification Service - Implements dual flow architecture for risky content detection

This service follows the established patterns from MCC and Policy Analysis services with:
- Normal flow: ≥50% reachable URLs using batches of 18 for Gemini classification
- Backup flow: <50% reachable using soft classification with text extraction from 6 pages maximum
- Both flows stop immediately when risky content is found
"""

import asyncio
import json
import traceback
import time
import csv
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import random

from sqlmodel import Session, select

from app.models.request_models import MccAnalysisRequest
from app.models.db_models import (
    WebsiteUrls, RiskyAnalysis, RiskyUrlAnalysis,
    ScrapeRequestTracker, get_current_time, engine
)
from app.gpt_models.gpt_prompts import GptPromptPicker
from app.gpt_models.gemini_model_wrapper.gemeni_utils import (
    get_optimized_gemini_response_for_task,
    get_gemini_response_legacy_no_tool,
    get_gemini_response_legacy
)
from app.utils.logger import ConsoleLogger
from app.utils.website_url_processor import get_urls_by_scrape_ref
from app.services.screenshot.url_utils import get_text_from_url_local
from app.services.url_classification import urlclassification_service


class RiskyClassificationService:
    """
    Risky Classification Service - handles complete risky content analysis with dual flow architecture
    """
    
    def __init__(self, scrape_request_ref_id: str, org_id: str = "default"):
        self.scrape_request_ref_id = scrape_request_ref_id
        self.org_id = org_id
        self.logger = ConsoleLogger(analysis_id=scrape_request_ref_id)

        self.db_session = Session(engine)

        # Configuration
        self.batch_size = 15  # FIXED: Use batches of 15 to be more conservative and ensure we stay well within Gemini's 20 URL limit
        self.reachability_threshold = 0.5  # 50%
        self.max_backup_urls = 6  # Maximum URLs for backup flow
        self.max_total_urls = 100  # Maximum URLs to process

        # ENHANCED: Timeout and circuit breaker configuration
        self.max_analysis_time = 1800  # 30 minutes maximum analysis time
        self.max_consecutive_failures = 3  # Maximum consecutive text extraction failures before circuit breaker
        self.text_extraction_timeout = 45  # Individual text extraction timeout
        self.consecutive_failures = 0  # Track consecutive failures
        
        # Priority order for backup flow (matching soft classification categories)
        # ENHANCED: Updated priority order to focus on risky content detection
        self.backup_priority_categories = [
            "terms_and_condition",  # High priority - often contains risky content policies
            "privacy_policy",       # High priority - may contain data handling risks
            "home_page",           # Medium priority - overview of business
            "about_us",            # Medium priority - business description
            "catalogue",           # Lower priority - product listings
        ]
        
        # Load risky keywords from CSV file
        self.risky_keywords = self._load_risky_keywords()
        self.logger.info(f"Loaded {len(self.risky_keywords)} risky keywords from CSV file")

        # Initialize reachability tracking
        self.reachable_urls = []
        self.unreachable_urls = []

    def __del__(self):
        """Clean up database connection"""
        if hasattr(self, 'db_session'):
            self.db_session.close()
    
    def _load_risky_keywords(self) -> List[Dict[str, str]]:
        """
        Load risky keywords from CSV file
        
        Returns:
            List[Dict[str, str]]: List of risky keywords with their definitions and ops input
        """
        risky_keywords = []
        csv_file_path = "/home/<USER>/WebReview_DS_API_24Jun/app/input/risky_keywords.csv"
        
        try:
            if not os.path.exists(csv_file_path):
                self.logger.warning(f"Risky keywords CSV file not found at {csv_file_path}")
                return risky_keywords
            
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.DictReader(file)
                
                for row in csv_reader:
                    # Only include keywords where "Ops input" is "Yes"
                    if row.get('Ops input', '').strip().lower() == 'yes':
                        risky_keywords.append({
                            'keyword': row.get('Risky Keyword', '').strip().lower(),
                            'definition': row.get('Definition', '').strip(),
                            'ops_input': row.get('Ops input', '').strip()
                        })
            
            self.logger.info(f"Successfully loaded {len(risky_keywords)} risky keywords from CSV")
            
            # Log some sample keywords for debugging
            if risky_keywords:
                sample_keywords = [kw['keyword'] for kw in risky_keywords[:5]]
                self.logger.info(f"Sample risky keywords: {sample_keywords}")
            
            return risky_keywords
            
        except Exception as e:
            self.logger.error(f"Error loading risky keywords from CSV: {str(e)}")
            return risky_keywords
    
    def get_risky_keywords_list(self) -> List[str]:
        """
        Get list of risky keywords for use in prompts
        
        Returns:
            List[str]: List of risky keywords
        """
        return [kw['keyword'] for kw in self.risky_keywords if kw['keyword']]
    
    async def process_risky_classification(self) -> Dict[str, Any]:
        """
        Main method to process risky classification with dual flow architecture
        ENHANCED: Added timeout protection and circuit breaker logic

        Returns:
            Dict[str, Any]: Processing results with status and details
        """
        start_time = time.time()

        try:
            self.logger.info("Starting risky classification analysis with timeout protection", {
                "max_analysis_time": f"{self.max_analysis_time}s",
                "max_consecutive_failures": self.max_consecutive_failures
            })

            # ENHANCED: Wrap the entire analysis in a timeout
            try:
                return await asyncio.wait_for(
                    self._execute_analysis_with_circuit_breaker(start_time),
                    timeout=self.max_analysis_time
                )
            except asyncio.TimeoutError:
                error_msg = f"Analysis timed out after {self.max_analysis_time} seconds"
                self.logger.error(error_msg)

                # Save timeout failure to database
                try:
                    await self.save_failure_results(
                        website="unknown",
                        error_msg=error_msg,
                        failure_code="FAILED_TIMEOUT",
                        flow_attempted="timeout_before_flow_selection",
                        reachability_percentage=0.0
                    )
                except Exception as save_error:
                    self.logger.error(f"Failed to save timeout failure: {save_error}")

                return {
                    "status": "FAILED",
                    "error": error_msg,
                    "failure_code": "FAILED_TIMEOUT",
                    "execution_time": time.time() - start_time
                }

        except Exception as e:
            error_msg = f"Unexpected error in risky classification: {str(e)}"
            self.logger.error(error_msg, error=e)

            return {
                "status": "FAILED",
                "error": error_msg,
                "failure_code": "FAILED_UNEXPECTED_ERROR",
                "execution_time": time.time() - start_time
            }

    async def _execute_analysis_with_circuit_breaker(self, start_time: float) -> Dict[str, Any]:
        """
        Execute the analysis with circuit breaker logic
        """
        try:
            # Check if URLs exist in database
            urls_exist, urls_data, flat_urls_list = self.check_urls_exist_in_db()

            if not urls_exist or not flat_urls_list:
                error_msg = "No URLs found for analysis"
                self.logger.error(error_msg)
                return {
                    "status": "FAILED",
                    "error": error_msg,
                    "execution_time": time.time() - start_time
                }

            website = urls_data["website"]

            # Limit URLs if necessary
            if len(flat_urls_list) > self.max_total_urls:
                self.logger.info(f"Limiting URLs from {len(flat_urls_list)} to {self.max_total_urls}")
                flat_urls_list = random.sample(flat_urls_list, self.max_total_urls)

            # FIXED: Step 1: Assess URL reachability without full classification
            reachability_percentage = await self.assess_url_reachability(website, flat_urls_list)

            self.logger.info(
                f"URL reachability assessment complete: {reachability_percentage:.1%}",
                {
                    "threshold": f"{self.reachability_threshold:.1%}",
                    "flow_selection": "normal" if reachability_percentage >= self.reachability_threshold else "backup"
                }
            )

            # Step 2: Choose flow based on reachability threshold
            if reachability_percentage >= self.reachability_threshold:
                # Normal flow: Use ONLY reachable URLs for batch risky analysis with early stop
                self.logger.info("Using normal flow (≥50% reachable URLs)")

                if not self.reachable_urls:
                    error_msg = "No reachable URLs available for normal flow"
                    self.logger.error(error_msg)
                    return {
                        "status": "FAILED",
                        "error": error_msg,
                        "execution_time": time.time() - start_time
                    }

                analysis_results = await self.execute_normal_flow_reachable_only(website, self.reachable_urls)
                flow_used = "normal"
            else:
                # Backup flow: Perform soft classification followed by hard classification
                self.logger.info("Using backup flow (<50% reachable URLs)")

                analysis_results = await self.execute_backup_flow_correct(website, flat_urls_list)
                flow_used = "backup"

            if not analysis_results:
                # ENHANCED: Check if this is due to circuit breaker or other issues
                if self.consecutive_failures >= self.max_consecutive_failures:
                    self.logger.warning(
                        f"Analysis failed due to circuit breaker - returning safe default result",
                        {
                            "consecutive_failures": self.consecutive_failures,
                            "flow_used": flow_used,
                            "website": website
                        }
                    )

                    # Return safe default result instead of failing
                    analysis_results = {
                        "is_risky": False,
                        "url_results": [],
                        "total_urls_analyzed": 0,
                        "flow_type": flow_used,
                        "early_stop": False,
                        "fallback_reason": "circuit_breaker_triggered",
                        "risk_categories": [],
                        "keywords_found": [],
                        "reason_for_risky": []
                    }

                    # Save the safe default result
                    analysis_id = await self.save_analysis_results(
                        website=website,
                        analysis_results=analysis_results,
                        flow_used=f"{flow_used}_circuit_breaker_fallback",
                        reachability_percentage=reachability_percentage,
                        total_urls_processed=len(flat_urls_list)
                    )

                    processing_time = time.time() - start_time

                    self.logger.info(
                        f"Risky classification completed with circuit breaker fallback in {processing_time:.2f} seconds",
                        {
                            "analysis_id": analysis_id,
                            "flow_used": f"{flow_used}_circuit_breaker_fallback",
                            "is_risky": False
                        }
                    )

                    return {
                        "status": "COMPLETED",
                        "analysis_id": analysis_id,
                        "flow_used": f"{flow_used}_circuit_breaker_fallback",
                        "is_risky": False,
                        "execution_time": processing_time
                    }
                else:
                    # Enhanced error handling with specific failure codes
                    if flow_used == "normal":
                        error_msg = f"Failed to complete normal flow analysis (≥50% URLs reachable)"
                        failure_code = "FAILED_NORMAL_FLOW"
                    elif flow_used == "backup":
                        error_msg = f"Failed to complete backup flow analysis (<50% URLs reachable)"
                        failure_code = "FAILED_BACKUP_FLOW"
                    else:
                        error_msg = f"Failed to complete {flow_used} flow analysis"
                        failure_code = "FAILED_DUAL_FLOW"

                    self.logger.error(error_msg)

                    # Update any existing record with failure details
                    await self.save_failure_results(
                        website=website,
                        error_msg=error_msg,
                        failure_code=failure_code,
                        flow_attempted=flow_used,
                        reachability_percentage=reachability_percentage
                    )

                    return {
                        "status": "FAILED",
                        "error": error_msg,
                        "failure_code": failure_code,
                        "execution_time": time.time() - start_time
                    }

            # Step 3: Save results to database
            analysis_id = await self.save_analysis_results(
                website=website,
                analysis_results=analysis_results,
                flow_used=flow_used,
                reachability_percentage=reachability_percentage,
                total_urls_processed=len(flat_urls_list)
            )

            processing_time = time.time() - start_time

            self.logger.info(
                f"Risky classification completed successfully in {processing_time:.2f} seconds",
                {
                    "analysis_id": analysis_id,
                    "flow_used": flow_used,
                    "is_risky": analysis_results.get("is_risky", False)
                }
            )

            return {
                "status": "COMPLETED",
                "analysis_id": analysis_id,
                "flow_used": flow_used,
                "is_risky": analysis_results.get("is_risky", False),
                "execution_time": processing_time
            }

        except Exception as e:
            error_msg = f"Error in risky classification: {str(e)}"
            self.logger.error(error_msg, error=e)
            self.logger.error("Full traceback:", data={"traceback": traceback.format_exc()})

            # Enhanced error handling with specific failure codes
            failure_code = "FAILED_CLASSIFICATION_ERROR"
            if "timeout" in str(e).lower():
                failure_code = "FAILED_TIMEOUT"
            elif "gemini" in str(e).lower() or "api" in str(e).lower():
                failure_code = "FAILED_GEMINI_ERROR"
            elif "url" in str(e).lower() or "reachable" in str(e).lower():
                failure_code = "FAILED_URL_UNREACHABLE"

            # Save failure results to database
            try:
                await self.save_failure_results(
                    website=website if 'website' in locals() else "unknown",
                    error_msg=error_msg,
                    failure_code=failure_code,
                    flow_attempted="both",
                    reachability_percentage=0.0
                )
            except Exception as save_error:
                self.logger.error(f"Failed to save error results: {save_error}")

            return {
                "status": "FAILED",
                "error": error_msg,
                "failure_code": failure_code,
                "execution_time": time.time() - start_time
            }
    
    def check_urls_exist_in_db(self) -> Tuple[bool, Optional[Dict], Optional[List[str]]]:
        """
        Check if URLs exist in database using scrape request ref id
        
        Returns:
            Tuple[bool, Optional[Dict], Optional[List[str]]]: (exists, urls_data, flat_urls_list)
        """
        self.logger.info(
            "Checking if URLs exist in database", 
            {"scrape_request_ref_id": self.scrape_request_ref_id}
        )
        
        try:
            # Use website_url_processor to get URLs by scrape request ref ID
            url_records = get_urls_by_scrape_ref(self.scrape_request_ref_id, self.db_session)
            
            if not url_records:
                self.logger.warning(
                    "No URLs found for scrape request",
                    {"scrape_request_ref_id": self.scrape_request_ref_id}
                )
                return False, None, None
            
            # Check if scrape request exists in tracker
            tracker = self.db_session.exec(
                select(ScrapeRequestTracker).where(
                    ScrapeRequestTracker.scrape_request_ref_id == self.scrape_request_ref_id,
                    ScrapeRequestTracker.org_id == self.org_id
                )
            ).first()
            
            if not tracker:
                self.logger.warning(
                    "Scrape request not found in tracker",
                    {"scrape_request_ref_id": self.scrape_request_ref_id}
                )
                return False, None, None
            
            # Organize URLs by depth
            urls_data = {
                "website": tracker.website,
                "parsed_urls": []
            }
            
            # Create a flat list of all URLs
            flat_urls_list = []
            
            urls_by_depth = {}
            for url_info in url_records:
                depth = url_info.get('depth', 1)
                url = url_info['url']
                if depth not in urls_by_depth:
                    urls_by_depth[depth] = []
                urls_by_depth[depth].append(url)
                flat_urls_list.append(url)
            
            # Convert to expected format
            for depth, urls_list in urls_by_depth.items():
                urls_data["parsed_urls"].append({
                    "url_depth": depth,
                    "urls": urls_list
                })
            
            self.logger.info(
                "URLs found in database",
                {
                    "website": tracker.website,
                    "total_depths": len(urls_by_depth),
                    "total_urls": len(url_records),
                    "urls_for_analysis": len(flat_urls_list)
                }
            )
            
            return True, urls_data, flat_urls_list
            
        except Exception as e:
            self.logger.error("Error checking URLs in database", error=e)
            return False, None, None

    async def assess_url_reachability(self, website: str, urls: List[str]) -> float:
        """
        Assess URL reachability using Gemini-based classification in batches of 15

        This method uses Gemini to check URL reachability to determine what percentage of URLs
        are reachable/accessible for analysis, which helps decide between Normal vs Backup flow.

        Args:
            website (str): Website URL
            urls (List[str]): List of URLs to assess

        Returns:
            float: Percentage of reachable URLs (0.0 to 1.0)
        """
        try:
            self.logger.info(f"Assessing reachability for {len(urls)} URLs using Gemini")

            if not urls:
                return 0.0

            reachable_urls = []
            unreachable_urls = []
            batch_size = 10  # Process URLs in batches of 15 to be more conservative and ensure we stay well within Gemini's 20 URL limit

            # Process URLs in batches
            for i in range(0, len(urls), batch_size):
                batch_urls = urls[i:i + batch_size]
                batch_number = i//batch_size + 1
                self.logger.info(f"Processing reachability batch {batch_number}: {len(batch_urls)} URLs")

                # Add delay between batches (except for the first batch) to respect rate limits
                if batch_number > 1:
                    delay_seconds = random.uniform(4, 6)  # 4-6 second delay for reachability checks
                    self.logger.info(f"Rate limiting: waiting {delay_seconds:.1f} seconds before reachability batch {batch_number}")
                    await asyncio.sleep(delay_seconds)

                # Create URL dictionary for the batch
                url_batch_dict = {str(idx): url for idx, url in enumerate(batch_urls)}

                # Get reachability classification from Gemini
                batch_reachable, batch_unreachable = await self._get_batch_reachability(website, url_batch_dict)

                if batch_reachable:
                    reachable_urls.extend(batch_reachable)
                if batch_unreachable:
                    unreachable_urls.extend(batch_unreachable)

            total_urls = len(urls)
            reachable_count = len(reachable_urls)
            reachability_percentage = reachable_count / total_urls if total_urls > 0 else 0.0

            # Log reachability counts and percentage decision in worker.log
            self.logger.info(
                f"URL reachability assessment complete: {reachable_count}/{total_urls} URLs reachable ({reachability_percentage:.1%})",
                {
                    "reachable_count": reachable_count,
                    "unreachable_count": len(unreachable_urls),
                    "total_urls": total_urls,
                    "reachability_percentage": f"{reachability_percentage:.1%}",
                    "flow_decision": "normal" if reachability_percentage >= self.reachability_threshold else "backup"
                }
            )

            # Store reachable/unreachable URLs for later use
            self.reachable_urls = reachable_urls
            self.unreachable_urls = unreachable_urls

            return reachability_percentage

        except Exception as e:
            self.logger.error(f"Error in reachability assessment: {str(e)}")
            # Default to backup flow on error
            return 0.0

    async def _get_batch_reachability(self, website: str, url_batch_dict: Dict[str, str]) -> Tuple[List[str], List[str]]:
        """
        Get reachability classification for a batch of URLs using Gemini

        Args:
            website (str): Website URL
            url_batch_dict (Dict[str, str]): Dictionary of index: URL pairs

        Returns:
            Tuple[List[str], List[str]]: (reachable_urls, unreachable_urls)
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                self.logger.info(f"Reachability check attempt {retry_count + 1} for \n {len(url_batch_dict)} URLs")

                # Create prompt for reachability classification
                reachability_prompt = GptPromptPicker.get_url_reachability_classification_prompt(website, url_batch_dict)

                self.logger.info("Debugging URL batch for reachability", {
                    "website": website,
                    "url_batch_size": len(url_batch_dict),
                    "url_batch_keys": list(url_batch_dict.keys()),
                    "task_type_to_be_sent": "url_reachability"
                })

                # Call Gemini API for reachability classification
                response = get_gemini_response_legacy(
                    reachability_prompt,
                    model_name="gemini-2.5-flash"  # Use specific task type for reachability
                )

                # Parse response
                reachability_results = self.parse_json_response(response)

                if not reachability_results or not isinstance(reachability_results, dict):
                    self.logger.warning(f"Failed to parse reachability response on attempt {retry_count + 1}", {
                        "response_preview": response[:200] if response else "None",
                        "response_type": type(response).__name__
                    })
                    raise Exception("Failed to parse reachability response")

                # Extract reachable and unreachable URLs
                reachable_urls = []
                unreachable_urls = []

                # The prompt expects arrays of indices, not dictionaries
                reachable_indices = reachability_results.get("reachable_urls", [])
                unreachable_indices = reachability_results.get("unreachable_urls", [])

                self.logger.info(f"Reachability response structure", {
                    "reachable_indices": reachable_indices,
                    "unreachable_indices": unreachable_indices,
                    "url_batch_dict_keys": list(url_batch_dict.keys()),
                    "total_urls_in_batch": len(url_batch_dict)
                })

                # Process reachable URLs using indices
                if isinstance(reachable_indices, list):
                    for idx in reachable_indices:
                        # Convert index to string to match url_batch_dict keys
                        idx_str = str(idx)
                        if idx_str in url_batch_dict:
                            url = url_batch_dict[idx_str]
                            if url and isinstance(url, str):
                                reachable_urls.append(url)

                # Process unreachable URLs using indices
                if isinstance(unreachable_indices, list):
                    for idx in unreachable_indices:
                        # Convert index to string to match url_batch_dict keys
                        idx_str = str(idx)
                        if idx_str in url_batch_dict:
                            url = url_batch_dict[idx_str]
                            if url and isinstance(url, str):
                                unreachable_urls.append(url)

                # Validate that we processed all URLs in the batch
                total_processed = len(reachable_urls) + len(unreachable_urls)
                total_expected = len(url_batch_dict)

                if total_processed != total_expected:
                    self.logger.warning(f"Reachability mismatch: processed {total_processed}, expected {total_expected}")
                    # Add missing URLs to unreachable list as fallback
                    processed_urls = set(reachable_urls + unreachable_urls)
                    for url in url_batch_dict.values():
                        if url not in processed_urls:
                            unreachable_urls.append(url)
                            self.logger.info(f"Added missing URL to unreachable: {url}")

                self.logger.info(
                    f"Reachability batch complete: {len(reachable_urls)} reachable, {len(unreachable_urls)} unreachable"
                )

                return reachable_urls, unreachable_urls

            except Exception as e:
                retry_count += 1
                self.logger.warning(f"Reachability check attempt {retry_count} failed: {str(e)}")

                if retry_count >= max_retries:
                    self.logger.error(f"All reachability check attempts failed for batch")
                    # Return all URLs as unreachable on failure
                    all_urls = list(url_batch_dict.values())
                    return [], all_urls

                await asyncio.sleep(2 ** retry_count)  # Exponential backoff

        return [], []

    async def get_url_classification_results(self, website: str, urls: List[str]) -> Optional[Dict]:
        """
        Get URL classification results using the URL classification service

        Args:
            website (str): Website URL
            urls (List[str]): List of URLs to classify

        Returns:
            Optional[Dict]: Classification results with soft_classified and hard_classified
        """
        try:
            self.logger.info("Starting URL classification using proper service methods")

            # FIXED: Use the URL classification service correctly
            from app.services.url_classification import urlclassification_service

            # Create service instance with proper parameters
            service = urlclassification_service(
                website=website,
                scrape_request_ref_id=self.scrape_request_ref_id,
                org_id=self.org_id
            )

            # Get URLs from database (organized by depth)
            depth_1_urls, depth_2_urls = service.get_urls_from_db()

            if not depth_1_urls and not depth_2_urls:
                self.logger.warning("No URLs found in database for classification")
                return None

            # Perform soft classification (correct method signature with all required parameters)
            output_df, soft_classified = service.soft_classify_urls(depth_1_urls, depth_2_urls, self.logger, self.scrape_request_ref_id, self.org_id)
            self.logger.info("✅ Soft classification completed", {"categories": list(soft_classified.keys())})

            # Perform hard classification (correct method signature)
            # FIXED: Hard classification now properly excludes social media URLs
            hard_classified = service.hard_classify_urls(soft_classified)
            self.logger.info("✅ Hard classification completed", {"categories": list(hard_classified.keys())})

            self.logger.info(
                "URL classification completed successfully",
                {
                    "soft_categories": list(soft_classified.keys()),
                    "hard_categories": list(hard_classified.keys()),
                    "social_media_properly_handled": True
                }
            )

            return {
                "soft_classified": soft_classified,
                "hard_classified": hard_classified
            }

        except Exception as e:
            self.logger.error(f"Error in URL classification: {str(e)}", error=e)
            return None

    # REMOVED: _classify_batch_reachability method - now using URL classification service

    async def execute_normal_flow_with_classification(self, website: str, hard_classified: Dict, soft_classified: Dict) -> Optional[Dict]:
        """
        Execute normal flow: batch processing using classification results with early stopping
        FIXED: Prioritizes hard classified URLs (policy/risky content) over social media URLs

        Args:
            website (str): Website URL
            hard_classified (Dict): Hard classification results (policy URLs only)
            soft_classified (Dict): Soft classification results (includes social media)

        Returns:
            Optional[Dict]: Analysis results with early stopping capability
        """
        try:
            # FIXED: Prioritize hard classified URLs (policy/risky content) over social media
            # Hard classification excludes social media URLs as per URL classification service
            priority_urls = []
            social_media_urls = []

            # Get policy/risky content URLs from hard classification (these are prioritized)
            for category, urls in hard_classified.items():
                if isinstance(urls, list):
                    priority_urls.extend(urls)
                    self.logger.info(f"Added {len(urls)} URLs from hard classified category '{category}'")

            # Get social media URLs from soft classification (lower priority)
            social_media_categories = ["instagram_page", "facebook_page", "youtube_page", "linkedin_page", "twitter_page", "pinterest_page", "x_page"]
            for category in social_media_categories:
                if category in soft_classified and isinstance(soft_classified[category], list):
                    social_media_urls.extend(soft_classified[category])
                    self.logger.info(f"Added {len(soft_classified[category])} URLs from social media category '{category}'")

            # Combine URLs with priority order: policy/risky content first, then social media
            all_urls = priority_urls + social_media_urls

            self.logger.info(f"Starting normal flow with {len(all_urls)} classified URLs", {
                "policy_risky_urls": len(priority_urls),
                "social_media_urls": len(social_media_urls),
                "total_urls": len(all_urls)
            })

            if not all_urls:
                self.logger.warning("No classified URLs available for normal flow")
                return None

            # Create batches of URLs
            batches = []
            for i in range(0, len(all_urls), self.batch_size):
                batch = all_urls[i:i+self.batch_size]
                batches.append(batch)

            self.logger.info(f"Created {len(batches)} batches for normal flow processing")

            all_url_results = []

            # Process each batch with early stopping and rate limiting
            for batch_idx, url_batch in enumerate(batches):
                self.logger.info(f"Processing batch {batch_idx + 1}/{len(batches)}")

                # Add delay between batches (except for the first batch) to respect rate limits
                if batch_idx > 0:
                    delay_seconds = random.uniform(4, 6)  # 4-6 second delay as per requirements
                    self.logger.info(f"Rate limiting: waiting {delay_seconds:.1f} seconds before next batch")
                    await asyncio.sleep(delay_seconds)

                batch_results = await self.analyze_url_batch_with_early_stop(website, url_batch)

                if not batch_results:
                    self.logger.warning(f"Batch {batch_idx + 1} analysis failed, continuing with next batch")
                    continue

                # Add batch results
                all_url_results.extend(batch_results.get("urls_analyzed", []))

                # Check for early stopping
                if batch_results.get("early_stop", False):
                    self.logger.info(
                        f"Early stopping triggered in batch {batch_idx + 1}: {batch_results.get('stop_reason')}"
                    )
                    break

            if not all_url_results:
                self.logger.error("No URL results obtained from normal flow")
                return None

            # Extract categories, keywords, and reasons from all URL results
            all_categories = []
            all_keywords = []
            all_reasons = []

            for url_result in all_url_results:
                # Extract categories (field name is "categories", not "risk_categories")
                categories = url_result.get("categories", [])
                if isinstance(categories, list):
                    all_categories.extend(categories)

                # Extract keywords
                keywords = url_result.get("keywords_found", [])
                if isinstance(keywords, list):
                    all_keywords.extend(keywords)

                # Extract reasons
                reasons = url_result.get("reason_for_risky", [])
                if isinstance(reasons, list):
                    all_reasons.extend(reasons)

            # Remove duplicates while preserving order
            unique_categories = list(dict.fromkeys(all_categories))
            unique_keywords = list(dict.fromkeys(all_keywords))
            unique_reasons = list(dict.fromkeys(all_reasons))

            # Generate overall risk assessment
            return {
                "is_risky": False,
                "url_results": all_url_results,
                "total_urls_analyzed": len(all_url_results),
                "flow_type": "normal",
                "early_stop": False,
                "risk_categories": unique_categories,  # Fixed: use aggregated categories
                "keywords_found": unique_keywords,     # Fixed: use aggregated keywords
                "reason_for_risky": unique_reasons     # Fixed: use aggregated reasons
            }

        except Exception as e:
            self.logger.error(f"Error in normal flow execution: {str(e)}", error=e)
            return None

    async def execute_normal_flow_reachable_only(self, website: str, reachable_urls: List[str]) -> Optional[Dict]:
        """
        Execute normal flow: send ONLY reachable URLs in batches of 15 to risky batch analysis with early stop

        Args:
            website (str): Website URL
            reachable_urls (List[str]): List of reachable URLs from reachability check

        Returns:
            Optional[Dict]: Analysis results with early stopping capability
        """
        try:
            self.logger.info(f"Starting normal flow with {len(reachable_urls)} reachable URLs")

            if not reachable_urls:
                self.logger.warning("No reachable URLs available for normal flow")
                return None

            # Create batches of 15 URLs
            batches = []
            for i in range(0, len(reachable_urls), self.batch_size):
                batch = reachable_urls[i:i+self.batch_size]
                batches.append(batch)

            self.logger.info(f"Created {len(batches)} batches for normal flow processing")

            all_url_results = []

            # Process each batch with early stopping
            for batch_idx, url_batch in enumerate(batches):
                self.logger.info(f"Processing batch {batch_idx + 1}/{len(batches)} with {len(url_batch)} URLs")

                # Add delay between batches (except for the first batch) to respect rate limits
                if batch_idx > 0:
                    delay_seconds = random.uniform(4, 6)  # 4-6 second delay
                    self.logger.info(f"Rate limiting: waiting {delay_seconds:.1f} seconds before next batch")
                    await asyncio.sleep(delay_seconds)

                batch_results = await self.analyze_url_batch_with_early_stop(website, url_batch)

                if not batch_results:
                    self.logger.warning(f"Batch {batch_idx + 1} analysis failed, continuing with next batch")
                    continue

                # Add batch results
                all_url_results.extend(batch_results.get("urls_analyzed", []))

                # Check for early stopping - CRITICAL: Stop immediately when risky content found
                if batch_results.get("early_stop", False):
                    self.logger.info(
                        f"Early stopping triggered in batch {batch_idx + 1}: {batch_results.get('stop_reason')}"
                    )
                    # Extract categories, keywords, and reasons from all URL results
                    all_categories = []
                    all_keywords = []
                    all_reasons = []

                    for url_result in all_url_results:
                        # Extract categories (field name is "categories", not "risk_categories")
                        categories = url_result.get("categories", [])
                        if isinstance(categories, list):
                            all_categories.extend(categories)

                        # Extract keywords
                        keywords = url_result.get("keywords_found", [])
                        if isinstance(keywords, list):
                            all_keywords.extend(keywords)

                        # Extract reasons
                        reasons = url_result.get("reason_for_risky", [])
                        if isinstance(reasons, list):
                            all_reasons.extend(reasons)

                    # Remove duplicates while preserving order
                    unique_categories = list(dict.fromkeys(all_categories))
                    unique_keywords = list(dict.fromkeys(all_keywords))
                    unique_reasons = list(dict.fromkeys(all_reasons))

                    # Return immediately with risky result
                    return {
                        "is_risky": True,
                        "url_results": all_url_results,
                        "total_urls_analyzed": len(all_url_results),
                        "flow_type": "normal",
                        "early_stop": True,
                        "stop_reason": batch_results.get('stop_reason'),
                        "risk_categories": unique_categories,  # Fixed: use aggregated categories
                        "keywords_found": unique_keywords,     # Fixed: use aggregated keywords
                        "reason_for_risky": unique_reasons     # Fixed: use aggregated reasons
                    }

            # If no risky content found after all batches, return not risky
            self.logger.info("Normal flow completed - no risky content found")

            # Extract categories, keywords, and reasons from all URL results
            all_categories = []
            all_keywords = []
            all_reasons = []

            for url_result in all_url_results:
                # Extract categories (field name is "categories", not "risk_categories")
                categories = url_result.get("categories", [])
                if isinstance(categories, list):
                    all_categories.extend(categories)

                # Extract keywords
                keywords = url_result.get("keywords_found", [])
                if isinstance(keywords, list):
                    all_keywords.extend(keywords)

                # Extract reasons
                reasons = url_result.get("reason_for_risky", [])
                if isinstance(reasons, list):
                    all_reasons.extend(reasons)

            # Remove duplicates while preserving order
            unique_categories = list(dict.fromkeys(all_categories))
            unique_keywords = list(dict.fromkeys(all_keywords))
            unique_reasons = list(dict.fromkeys(all_reasons))

            return {
                "is_risky": False,
                "url_results": all_url_results,
                "total_urls_analyzed": len(all_url_results),
                "flow_type": "normal",
                "early_stop": False,
                "risk_categories": unique_categories,  # Fixed: use aggregated categories
                "keywords_found": unique_keywords,     # Fixed: use aggregated keywords
                "reason_for_risky": unique_reasons     # Fixed: use aggregated reasons
            }

        except Exception as e:
            self.logger.error(f"Error in normal flow execution: {str(e)}", error=e)
            return None

    async def execute_backup_flow_with_classification(self, website: str, soft_classified: Dict) -> Optional[Dict]:
        """
        Execute backup flow: text extraction and analysis using classification results with priority ordering

        Args:
            website (str): Website URL
            soft_classified (Dict): Soft classification results

        Returns:
            Optional[Dict]: Analysis results with early stopping capability
        """
        try:
            # FIXED: Use classification results to prioritize URLs
            prioritized_urls = self.prioritize_urls_from_classification(soft_classified)

            self.logger.info(f"Starting backup flow with {len(prioritized_urls)} classified URLs")

            # Limit to maximum backup URLs
            urls_to_process = prioritized_urls[:self.max_backup_urls]

            self.logger.info(
                f"Selected {len(urls_to_process)} URLs for backup flow processing",
                {"priority_order": [url for url in urls_to_process]}
            )

            url_results = []

            # Process URLs one by one with early stopping and enhanced logging
            for idx, url in enumerate(urls_to_process):
                self.logger.info(
                    f"Processing URL {idx + 1}/{len(urls_to_process)}: {url}",
                    {"backup_flow": True, "priority_index": idx}
                )

                # Extract text content with enhanced error handling
                extracted_text = await self.extract_text_with_retry(url)

                if not extracted_text or len(extracted_text.strip()) < 100:
                    self.logger.warning(
                        f"Insufficient text extracted from {url}, skipping",
                        {"text_length": len(extracted_text) if extracted_text else 0}
                    )
                    continue

                # Analyze text for risky content
                text_analysis = await self.analyze_text_for_risk(website, url, extracted_text)

                if not text_analysis:
                    self.logger.warning(f"Text analysis failed for {url}, continuing")
                    continue

                # Add extraction metadata and backup flow indicators
                text_analysis["extracted_text_length"] = len(extracted_text)
                text_analysis["url_priority_index"] = idx
                text_analysis["flow_type"] = "backup"
                text_analysis["text_extraction_method"] = "backup_flow"

                url_results.append(text_analysis)

                # ENHANCED: Check for early stopping (risky content found)
                if text_analysis.get("is_risky", False):
                    self.logger.info(
                        f"🚨 Early stopping triggered: risky content detected in {url}",
                        {
                            "risk_categories": text_analysis.get("risk_categories", []),
                            "keywords_found": text_analysis.get("keywords_found", []),
                            "urls_processed_before_stop": idx + 1,
                            "total_urls_available": len(urls_to_process)
                        }
                    )
                    break
                else:
                    self.logger.info(f"✅ URL {idx + 1} analyzed - no risky content detected")

            if not url_results:
                self.logger.error("No URL results obtained from backup flow")
                return None

            # Generate overall risk assessment
            overall_results = await self.generate_overall_risk_assessment(website, url_results)

            if not overall_results:
                self.logger.error("Failed to generate overall risk assessment")
                return None

            # Combine results
            final_results = {
                **overall_results,
                "url_results": url_results,
                "total_urls_analyzed": len(url_results),
                "flow_type": "backup"
            }

            self.logger.info(
                f"Backup flow completed successfully",
                {
                    "urls_analyzed": len(url_results),
                    "is_risky": final_results.get("is_risky", False)
                }
            )

            return final_results

        except Exception as e:
            self.logger.error(f"Error in backup flow execution: {str(e)}", error=e)
            return None

    async def execute_backup_flow_correct(self, website: str, urls: List[str]) -> Optional[Dict]:
        """
        Execute correct backup flow: soft classification → hard classification (excluding social media) →
        text extraction from exactly 6 priority URLs with early stopping

        Args:
            website (str): Website URL
            urls (List[str]): List of URLs to process

        Returns:
            Optional[Dict]: Analysis results with early stopping capability
        """
        try:
            self.logger.info(f"Starting backup flow with {len(urls)} URLs")

            # Step 1: Get URL classification results (soft + hard)
            classification_results = await self.get_url_classification_results(website, urls)

            if not classification_results:
                self.logger.error("Failed to get URL classification results for backup flow")
                return None

            soft_classified = classification_results.get("soft_classified", {})
            hard_classified = classification_results.get("hard_classified", {})

            # Step 2: Exclude social media URLs from hard classification
            non_social_hard_classified = self._exclude_social_media_from_hard_classification(soft_classified)

            self.logger.info(
                f"soft classification filtering: {len(hard_classified)} total categories, "
                f"{len(non_social_hard_classified)} non-social categories"
            )

            # Step 3: Select exactly 6 priority URLs for text extraction
            priority_urls = self._select_priority_urls_for_backup(non_social_hard_classified)

            if not priority_urls:
                self.logger.warning("No priority URLs found for backup flow text extraction")
                # Return default "not risky" result
                return {
                    "is_risky": False,
                    "url_results": [],
                    "total_urls_analyzed": 0,
                    "flow_type": "backup",
                    "early_stop": False,
                    "risk_categories": [],
                    "keywords_found": [],
                    "reason_for_risky": []
                }

            self.logger.info(f"Selected {len(priority_urls)} priority URLs for text extraction")

            # Step 4: Extract text and analyze each URL with early stopping and circuit breaker
            url_results = []
            successful_extractions = 0

            for idx, url in enumerate(priority_urls):
                self.logger.info(f"Processing priority URL {idx + 1}/{len(priority_urls)}: {url}")

                # ENHANCED: Check circuit breaker before processing
                if self.consecutive_failures >= self.max_consecutive_failures:
                    self.logger.error(
                        f"Circuit breaker triggered in backup flow after {self.consecutive_failures} consecutive failures",
                        {"remaining_urls": len(priority_urls) - idx}
                    )
                    break

                # Extract text content
                extracted_text = await self.extract_text_with_retry(url)

                if not extracted_text or len(extracted_text.strip()) < 50:
                    self.logger.warning(f"Insufficient text extracted from {url}, skipping")
                    continue

                # ENHANCED: Track successful extractions
                successful_extractions += 1

                # Analyze text for risky content
                text_analysis = await self.analyze_text_for_risk(website, url, extracted_text)

                if text_analysis:
                    url_results.append(text_analysis)

                    # CRITICAL: Early stopping - if risky content found, stop immediately
                    if text_analysis.get("is_risky", False):
                        self.logger.info(f"Early stopping triggered: risky content found in {url}")

                        # Extract categories, keywords, and reasons from all URL results
                        all_categories = []
                        all_keywords = []
                        all_reasons = []

                        for url_result in url_results:
                            # Extract categories (field name is "categories", not "risk_categories")
                            categories = url_result.get("categories", [])
                            if isinstance(categories, list):
                                all_categories.extend(categories)

                            # Extract keywords
                            keywords = url_result.get("keywords_found", [])
                            if isinstance(keywords, list):
                                all_keywords.extend(keywords)

                            # Extract reasons
                            reasons = url_result.get("reason_for_risky", [])
                            if isinstance(reasons, list):
                                all_reasons.extend(reasons)

                        # Remove duplicates while preserving order
                        unique_categories = list(dict.fromkeys(all_categories))
                        unique_keywords = list(dict.fromkeys(all_keywords))
                        unique_reasons = list(dict.fromkeys(all_reasons))

                        return {
                            "is_risky": True,
                            "url_results": url_results,
                            "total_urls_analyzed": len(url_results),
                            "flow_type": "backup",
                            "early_stop": True,
                            "stop_reason": f"Risky content found in {url}",
                            "risk_categories": unique_categories,  # Fixed: use aggregated categories
                            "keywords_found": unique_keywords,     # Fixed: use aggregated keywords
                            "reason_for_risky": unique_reasons     # Fixed: use aggregated reasons
                        }

            # ENHANCED: Fallback logic when no text could be extracted
            if successful_extractions == 0:
                self.logger.warning(
                    "No text could be extracted from any priority URLs - returning safe default",
                    {
                        "total_urls_attempted": len(priority_urls),
                        "consecutive_failures": self.consecutive_failures,
                        "circuit_breaker_triggered": self.consecutive_failures >= self.max_consecutive_failures
                    }
                )
                return {
                    "is_risky": False,
                    "url_results": [],
                    "total_urls_analyzed": 0,
                    "flow_type": "backup",
                    "early_stop": False,
                    "fallback_reason": "no_text_extracted",
                    "risk_categories": [],
                    "keywords_found": [],
                    "reason_for_risky": []
                }

            # If no risky content found after processing all priority URLs
            self.logger.info("Backup flow completed - no risky content found")

            # Extract categories, keywords, and reasons from all URL results
            all_categories = []
            all_keywords = []
            all_reasons = []

            for url_result in url_results:
                # Extract categories (field name is "categories", not "risk_categories")
                categories = url_result.get("categories", [])
                if isinstance(categories, list):
                    all_categories.extend(categories)

                # Extract keywords
                keywords = url_result.get("keywords_found", [])
                if isinstance(keywords, list):
                    all_keywords.extend(keywords)

                # Extract reasons
                reasons = url_result.get("reason_for_risky", [])
                if isinstance(reasons, list):
                    all_reasons.extend(reasons)

            # Remove duplicates while preserving order
            unique_categories = list(dict.fromkeys(all_categories))
            unique_keywords = list(dict.fromkeys(all_keywords))
            unique_reasons = list(dict.fromkeys(all_reasons))

            return {
                "is_risky": False,
                "url_results": url_results,
                "total_urls_analyzed": len(url_results),
                "flow_type": "backup",
                "early_stop": False,
                "risk_categories": unique_categories,  # Fixed: use aggregated categories
                "keywords_found": unique_keywords,     # Fixed: use aggregated keywords
                "reason_for_risky": unique_reasons     # Fixed: use aggregated reasons
            }

        except Exception as e:
            self.logger.error(f"Error in backup flow execution: {str(e)}", error=e)
            return None

    def _exclude_social_media_from_hard_classification(self, hard_classified: Dict) -> Dict:
        """
        Exclude social media URLs from hard classification results

        Args:
            hard_classified (Dict): Hard classification results

        Returns:
            Dict: Filtered hard classification without social media URLs
        """
        social_media_categories = [
            "instagram_page", "facebook_page", "youtube_page",
            "linkedin_page", "twitter_page", "pinterest_page", "x_page"
        ]

        non_social_classified = {}
        for category, urls in hard_classified.items():
            if category not in social_media_categories:
                non_social_classified[category] = urls

        return non_social_classified

    def _select_priority_urls_for_backup(self, hard_classified: Dict) -> List[str]:
        """
        Select exactly 6 priority URLs for backup flow text extraction
        Priority order: home_page, about_us, terms_and_conditions, return_and_cancellation, products, catalogue

        Args:
            hard_classified (Dict): Non-social hard classification results

        Returns:
            List[str]: List of priority URLs (max 6)
        """
        priority_categories = [
            "home_page",
            "about_us",
            "terms_and_condition",
            "privacy_policy",
            "services",
        ]

        selected_urls = []

        for category in priority_categories:
            if len(selected_urls) >= 6:
                break

            if category in hard_classified and isinstance(hard_classified[category], list):
                category_urls = hard_classified[category]
                if category_urls:
                    # Take the first URL from each category
                    selected_urls.append(category_urls[0])
                    self.logger.info(f"Selected URL from {category}: {category_urls[0]}")

        # If we still need more URLs, add from products/catalogue categories
        if len(selected_urls) < 6:
            for category in ["products", "catalogue"]:
                if len(selected_urls) >= 6:
                    break

                if category in hard_classified and isinstance(hard_classified[category], list):
                    category_urls = hard_classified[category]
                    # Add additional URLs from these categories if available
                    for url in category_urls[1:]:  # Skip first URL if already added
                        if len(selected_urls) >= 6:
                            break
                        if url not in selected_urls:
                            selected_urls.append(url)
                            self.logger.info(f"Selected additional URL from {category}: {url}")

        return selected_urls[:6]  # Ensure exactly 6 URLs maximum

    def prioritize_urls_from_classification(self, soft_classified: Dict) -> List[str]:
        """
        ENHANCED: Prioritize URLs from classification results with improved logic for backup flow

        Args:
            soft_classified (Dict): Soft classification results

        Returns:
            List[str]: Prioritized list of URLs based on classification and risky content potential
        """
        try:
            prioritized = []
            category_stats = {}

            # First, add URLs from priority categories in order (highest risk potential first)
            for category in self.backup_priority_categories:
                if category in soft_classified and isinstance(soft_classified[category], list):
                    category_urls = soft_classified[category]
                    # Remove duplicates while preserving order
                    unique_urls = []
                    for url in category_urls:
                        if url not in prioritized:
                            unique_urls.append(url)
                            prioritized.append(url)

                    category_stats[category] = len(unique_urls)
                    self.logger.info(f"✅ Added {len(unique_urls)} URLs from priority category '{category}'")

            # Add URLs from other categories (including social media and miscellaneous)
            other_category_count = 0
            for category, category_urls in soft_classified.items():
                if category not in self.backup_priority_categories and isinstance(category_urls, list):
                    unique_urls_added = 0
                    for url in category_urls:
                        if url not in prioritized:
                            prioritized.append(url)
                            unique_urls_added += 1

                    if unique_urls_added > 0:
                        category_stats[category] = unique_urls_added
                        other_category_count += unique_urls_added
                        self.logger.info(f"➕ Added {unique_urls_added} URLs from category '{category}'")

            self.logger.info(
                f"🎯 URL prioritization complete for backup flow",
                {
                    "total_prioritized": len(prioritized),
                    "priority_categories_used": len([c for c in self.backup_priority_categories if c in category_stats]),
                    "other_categories_used": len(category_stats) - len([c for c in self.backup_priority_categories if c in category_stats]),
                    "category_breakdown": category_stats,
                    "max_backup_urls": self.max_backup_urls,
                    "will_process": min(len(prioritized), self.max_backup_urls)
                }
            )

            return prioritized

        except Exception as e:
            self.logger.error(f"Error in classification prioritization: {str(e)}")
            # Fallback: return all URLs from classification results
            all_urls = []
            for category_urls in soft_classified.values():
                if isinstance(category_urls, list):
                    all_urls.extend(category_urls)
            # Remove duplicates in fallback
            return list(dict.fromkeys(all_urls))
    # REMOVED: get_soft_classification_results and _fallback_prioritize_urls methods
    # Now using URL classification service results directly

    async def analyze_url_batch_with_early_stop(self, website: str, url_batch: List[str]) -> Optional[Dict]:
        """
        Analyze a batch of URLs with early stopping capability

        Args:
            website (str): Website URL
            url_batch (List[str]): Batch of URLs to analyze

        Returns:
            Optional[Dict]: Analysis results with early_stop flag
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                self.logger.info(
                    f"Batch analysis with early stop attempt {retry_count + 1}",
                    {"urls_count": len(url_batch)}
                )

                # Create prompt for batch analysis with early stopping
                # Convert URLs list to dictionary for the prompt function
                url_batch_dict = {i: url for i, url in enumerate(url_batch)}
                self.logger.info("URL batch for early stop analysis", {
                    "website": website,
                    "url_batch_size": len(url_batch_dict),
                    "url_batch_keys": list(url_batch_dict.keys()),
                    "task_type_to_be_sent": "risky batch analysis prompt"
                })

                batch_prompt = GptPromptPicker.get_risky_batch_analysis_prompt_with_early_stop(website, url_batch_dict)


                self.logger.info("sending get_risky_batch_analysis_prompt_with_early_stop",{
                    "Website": website,
                    "url_batch_size": len(url_batch_dict),
                    "url_batch_keys": list(url_batch_dict.keys()),
                    "task_type_to_be_sent": "risky batch analysis prompt"
                } )
                # Call Gemini API for batch analysis (critical reasoning - use advanced model)
                response = get_gemini_response_legacy(
                    batch_prompt, 
                    model_name="gemini-2.5-flash" # Use gemini-2.5-flash for reasoning tasks
                )

                # Parse response
                batch_results = self.parse_json_response(response)

                if not batch_results or not isinstance(batch_results, dict):
                    self.logger.warning(f"Failed to parse batch analysis response on attempt {retry_count + 1}", {
                        "response_preview": response[:200] if response else "None",
                        "response_type": type(response).__name__
                    })
                    raise Exception("Failed to parse batch analysis response")

                # Validate response structure
                if "urls_analyzed" not in batch_results:
                    raise Exception("Invalid batch analysis response structure")

                self.logger.info(
                    "Batch analysis completed successfully",
                    {
                        "urls_analyzed": len(batch_results.get("urls_analyzed", [])),
                        "early_stop": batch_results.get("early_stop", False)
                    }
                )

                return batch_results

            except Exception as e:
                retry_count += 1
                self.logger.warning(
                    f"Batch analysis attempt {retry_count} failed: {str(e)}"
                )

                if retry_count < max_retries:
                    await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                else:
                    self.logger.error(
                        "All batch analysis attempts failed",
                        data={"max_retries": max_retries, "last_error": str(e)}
                    )
                    return None

        return None

    async def extract_text_with_retry(self, url: str) -> Optional[str]:
        """
        Extract text with retry logic and circuit breaker protection
        ENHANCED: Added circuit breaker to prevent infinite loops
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # ENHANCED: Check circuit breaker before attempting extraction
                if self.consecutive_failures >= self.max_consecutive_failures:
                    self.logger.error(
                        f"Circuit breaker triggered: {self.consecutive_failures} consecutive failures",
                        {"url": url, "max_failures": self.max_consecutive_failures}
                    )
                    return None

                self.logger.info(f"Text extraction attempt {retry_count + 1} for {url}")

                # Use text extraction with proxy support - REDUCED timeout for faster failures
                extracted_text = await get_text_from_url_local(
                    url=url,
                    endpoint_type="risky_classification",
                    org_id=self.org_id,
                    timeout=self.text_extraction_timeout  # Use configurable timeout
                )

                if extracted_text and len(extracted_text.strip()) > 100:
                    # Truncate to stay within token limits (approximately 90k tokens)
                    max_chars = 90000
                    if len(extracted_text) > max_chars:
                        extracted_text = extracted_text[:max_chars] + "... [TRUNCATED]"

                    self.logger.info(
                        f"Text extraction successful for {url}",
                        {"text_length": len(extracted_text)}
                    )

                    # ENHANCED: Reset consecutive failures on success
                    self.consecutive_failures = 0
                    return extracted_text
                else:
                    raise Exception("Insufficient text content extracted")

            except Exception as e:
                retry_count += 1
                self.logger.warning(
                    f"Text extraction attempt {retry_count} failed for {url}: {str(e)}"
                )

                if retry_count < max_retries:
                    await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                else:
                    # ENHANCED: Increment consecutive failures counter
                    self.consecutive_failures += 1
                    self.logger.error(
                        f"All text extraction attempts failed for {url}",
                        data={
                            "max_retries": max_retries,
                            "last_error": str(e),
                            "consecutive_failures": self.consecutive_failures
                        }
                    )
                    return None

        return None

    async def analyze_text_for_risk(self, website: str, url: str, extracted_text: str) -> Optional[Dict]:
        """
        Analyze extracted text content for risky indicators

        Args:
            website (str): Website URL
            url (str): Specific URL being analyzed
            extracted_text (str): Extracted text content

        Returns:
            Optional[Dict]: Risk analysis results
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                self.logger.info(f"Text risk analysis attempt {retry_count + 1} for {url}")

                # Create prompt for text analysis
                text_prompt = GptPromptPicker.get_risky_text_analysis_prompt(website, url, extracted_text)

                # Call Gemini API for text risk analysis (critical reasoning - use advanced model)
                response = get_gemini_response_legacy_no_tool(
                    text_prompt, 
                    model_name="gemini-2.5-flash"  # Use gemini-2.5-flash for reasoning tasks
                )

                # Parse response
                text_results = self.parse_json_response(response)

                if not text_results or not isinstance(text_results, dict):
                    raise Exception("Failed to parse text analysis response")

                self.logger.info(
                    f"Text risk analysis completed for {url}",
                    {
                        "is_risky": text_results.get("is_risky", False),
                        "risk_categories": text_results.get("risk_categories", [])
                    }
                )

                return text_results

            except Exception as e:
                retry_count += 1
                self.logger.warning(
                    f"Text analysis attempt {retry_count} failed for {url}: {str(e)}"
                )

                if retry_count < max_retries:
                    await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                else:
                    self.logger.error(
                        f"All text analysis attempts failed for {url}",
                        data={"max_retries": max_retries, "last_error": str(e)}
                    )
                    return None

        return None

    async def generate_overall_risk_assessment(self, website: str, url_results: List[Dict]) -> Optional[Dict]:
        """
        Generate overall risk assessment based on URL analysis results

        Args:
            website (str): Website URL
            url_results (List[Dict]): Individual URL analysis results

        Returns:
            Optional[Dict]: Overall risk assessment
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                self.logger.info(f"Overall risk assessment attempt {retry_count + 1}")

                # Create prompt for overall analysis
                overall_prompt = GptPromptPicker.get_risky_overall_analysis_prompt(website, url_results)

                # Call Gemini API for overall risk assessment (critical reasoning - use advanced model)
                response = get_optimized_gemini_response_for_task(
                    overall_prompt, 
                    task_type="post_text_extraction"  # Use gemini-2.5-flash for reasoning tasks
                )

                # Parse response
                overall_results = self.parse_json_response(response)

                if not overall_results or not isinstance(overall_results, dict):
                    raise Exception("Failed to parse overall analysis response")

                self.logger.info(
                    "Overall risk assessment completed",
                    {
                        "is_risky": overall_results.get("is_risky", False),
                        "risk_categories": overall_results.get("risk_categories", [])
                    }
                )

                return overall_results

            except Exception as e:
                retry_count += 1
                self.logger.warning(
                    f"Overall assessment attempt {retry_count} failed: {str(e)}"
                )

                if retry_count < max_retries:
                    await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                else:
                    self.logger.error(
                        "All overall assessment attempts failed",
                        data={"max_retries": max_retries, "last_error": str(e)}
                    )
                    return None

        return None

    def parse_json_response(self, response: str) -> Optional[Dict]:
        """
        Parse JSON response from Gemini API with enhanced error handling

        Args:
            response (str): Raw response from Gemini

        Returns:
            Optional[Dict]: Parsed JSON data
        """
        try:
            # Validate input
            if not response or not isinstance(response, str):
                self.logger.error("Invalid response provided", {"response_type": type(response)})
                return None

            # Clean the response
            response_text = response.strip()

            # Log the raw response for debugging
            self.logger.info("Raw response received", {"response_preview": response_text[:500]})

            # Handle error responses from Gemini
            if response_text.startswith("Error:") or response_text.startswith("No response from Gemini"):
                self.logger.error("Gemini API returned error response", {"error_response": response_text})
                return None

            # Remove markdown code blocks if present
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.startswith("```"):
                response_text = response_text[3:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]

            response_text = response_text.strip()

            # Handle empty response
            if not response_text:
                self.logger.warning("Empty response text after cleaning")
                return None

            # Try to find JSON in the response if it's mixed with other text
            json_start = response_text.find('{')
            json_end = response_text.rfind('}')

            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end + 1]
                self.logger.info("Extracted JSON from mixed response", {"json_preview": json_text[:200]})
            else:
                json_text = response_text

            # Parse JSON
            parsed_data = json.loads(json_text)

            # Validate that we got a dictionary (not a list or other type)
            if not isinstance(parsed_data, dict):
                self.logger.error(
                    "API returned non-dictionary response",
                    {
                        "response_type": type(parsed_data).__name__,
                        "response_preview": str(parsed_data)[:200] if parsed_data else "None"
                    }
                )
                return None

            # Convert string boolean values to actual booleans
            parsed_data = self._convert_string_booleans(parsed_data)

            self.logger.info("Successfully parsed JSON response", {"keys": list(parsed_data.keys())})
            return parsed_data

        except json.JSONDecodeError as e:
            self.logger.error(
                "Error parsing JSON response",
                {
                    "json_error": str(e),
                    "response_preview": response_text[:500] if 'response_text' in locals() else response[:500],
                    "char_position": getattr(e, 'pos', 'unknown')
                }
            )
            return None
        except Exception as e:
            self.logger.error(f"Error processing response: {e}", {"error": str(e), "error_type": type(e).__name__})
            return None

    def _convert_string_booleans(self, data):
        """
        Recursively convert string boolean values to actual booleans

        Args:
            data: The data structure to process

        Returns:
            The data structure with string booleans converted to actual booleans
        """
        # Handle None or non-dict/list data first
        if data is None:
            return data

        if isinstance(data, dict):
            converted = {}
            for key, value in data.items():
                if key == "is_risky" and isinstance(value, str):
                    # Convert string boolean to actual boolean
                    if value.lower() in ["yes", "true", "1"]:
                        converted[key] = True
                    elif value.lower() in ["no", "false", "0"]:
                        converted[key] = False
                    else:
                        # Log unexpected value and default to False
                        self.logger.warning(f"Unexpected boolean string value for {key}: {value}, defaulting to False")
                        converted[key] = False
                else:
                    # Recursively process nested structures
                    converted[key] = self._convert_string_booleans(value)
            return converted
        elif isinstance(data, list):
            return [self._convert_string_booleans(item) for item in data]
        else:
            # Return primitive types as-is (str, int, float, bool, etc.)
            return data

    async def save_analysis_results(
        self,
        website: str,
        analysis_results: Dict,
        flow_used: str,
        reachability_percentage: float,
        total_urls_processed: int
    ) -> int:
        """
        Save risky classification analysis results to database

        DUPLICATION FIX: Updates existing router-created record instead of creating new one

        Args:
            website (str): Website URL
            analysis_results (Dict): Analysis results
            flow_used (str): Flow type used ("normal" or "backup")
            reachability_percentage (float): Percentage of reachable URLs
            total_urls_processed (int): Total URLs processed

        Returns:
            int: Analysis ID
        """
        self.logger.info("Saving risky classification results to database")

        try:
            # DUPLICATION FIX: Find existing router-created record first
            existing_analysis = self.db_session.exec(
                select(RiskyAnalysis).where(
                    RiskyAnalysis.scrape_request_ref_id == self.scrape_request_ref_id,
                    RiskyAnalysis.processing_status.in_(["PENDING", "PROCESSING"])
                )
            ).first()

            if existing_analysis:
                # UPDATE existing record with complete analysis results
                self.logger.info(f"DUPLICATION FIX: Updating existing record ID {existing_analysis.id} instead of creating new one")

                existing_analysis.website = website
                existing_analysis.analysis_flow_used = flow_used
                existing_analysis.reachability_percentage = reachability_percentage
                existing_analysis.total_urls_processed = total_urls_processed
                existing_analysis.overall_result = json.dumps(analysis_results)
                existing_analysis.is_risky = analysis_results.get("is_risky", False)
                existing_analysis.risk_categories = json.dumps(analysis_results.get("risk_categories", []))
                existing_analysis.keywords_found = json.dumps(analysis_results.get("keywords_found", []))
                existing_analysis.reason_for_risky = json.dumps(analysis_results.get("reason_for_risky", []))
                existing_analysis.analysis_details = json.dumps(analysis_results)
                existing_analysis.processing_status = "COMPLETED"
                existing_analysis.completed_at = get_current_time()
                existing_analysis.org_id = self.org_id

                analysis_id = existing_analysis.id

                self.logger.info(f"DUPLICATION FIX: Updated existing analysis record {analysis_id}")

            else:
                # Create new record only if none exists (fallback)
                self.logger.info("DUPLICATION FIX: No existing record found, creating new one")

                risky_analysis = RiskyAnalysis(
                    website=website,
                    scrape_request_ref_id=self.scrape_request_ref_id,
                    analysis_flow_used=flow_used,
                    reachability_percentage=reachability_percentage,
                    total_urls_processed=total_urls_processed,
                    overall_result=json.dumps(analysis_results),
                    is_risky=analysis_results.get("is_risky", False),
                    risk_categories=json.dumps(analysis_results.get("risk_categories", [])),
                    keywords_found=json.dumps(analysis_results.get("keywords_found", [])),
                    reason_for_risky=json.dumps(analysis_results.get("reason_for_risky", [])),
                    analysis_details=json.dumps(analysis_results),
                    processing_status="COMPLETED",
                    created_at=get_current_time(),
                    started_at=get_current_time(),
                    completed_at=get_current_time(),
                    org_id=self.org_id
                )

                self.db_session.add(risky_analysis)
                self.db_session.flush()  # Get ID without committing

                analysis_id = risky_analysis.id

            # Save URL analysis records
            url_results = analysis_results.get("url_results", [])
            for url_result in url_results:
                url = url_result.get("url", "")
                if url:
                    url_analysis = RiskyUrlAnalysis(
                        risky_analysis_id=analysis_id,
                        scrape_request_ref_id=self.scrape_request_ref_id,
                        url=url,
                        is_risky=url_result.get("is_risky", False),
                        risk_categories=json.dumps(url_result.get("risk_categories", [])),
                        analysis_details=json.dumps(url_result),
                        extracted_text=url_result.get("extracted_text", None),
                        text_extraction_method=url_result.get("text_extraction_method", None),
                        org_id=self.org_id
                    )
                    self.db_session.add(url_analysis)

            # Update scrape request tracker
            tracker = self.db_session.exec(
                select(ScrapeRequestTracker).where(
                    ScrapeRequestTracker.scrape_request_ref_id == self.scrape_request_ref_id,
                    ScrapeRequestTracker.analysis_type == "risky",
                    ScrapeRequestTracker.org_id == self.org_id
                )
            ).first()

            if tracker:
                tracker.analysis_id = analysis_id
                tracker.status = "COMPLETED"
                tracker.completed_at = get_current_time()

            # Commit all changes
            self.db_session.commit()

            # DUPLICATION VERIFICATION: Check final record count
            final_verification = self.db_session.exec(
                select(RiskyAnalysis).where(
                    RiskyAnalysis.scrape_request_ref_id == self.scrape_request_ref_id
                )
            ).all()

            self.logger.info(f"DUPLICATION VERIFICATION: Final record count for ref_id {self.scrape_request_ref_id}: {len(final_verification)}")

            if len(final_verification) > 1:
                self.logger.error(f"DUPLICATION STILL EXISTS: Found {len(final_verification)} records after save")
                for record in final_verification:
                    self.logger.error(f"  - Record ID {record.id}: status={record.processing_status}, flow={record.analysis_flow_used}")
            else:
                self.logger.info(f"DUPLICATION FIX SUCCESS: Only 1 record exists after save")

            self.logger.info(
                "Risky classification results saved successfully",
                {
                    "analysis_id": analysis_id,
                    "url_results_count": len(url_results),
                    "flow_used": flow_used,
                    "record_action": "updated" if existing_analysis else "created"
                }
            )

            return analysis_id

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Error saving analysis results: {str(e)}", error=e)
            raise

    async def save_failure_results(
        self,
        website: str,
        error_msg: str,
        failure_code: str,
        flow_attempted: str,
        reachability_percentage: float
    ) -> Optional[int]:
        """
        Save failure results to database with enhanced default values

        Args:
            website (str): Website URL
            error_msg (str): Error message
            failure_code (str): Specific failure code
            flow_attempted (str): Flow that was attempted
            reachability_percentage (float): Percentage of reachable URLs

        Returns:
            Optional[int]: Analysis ID if saved successfully
        """
        self.logger.info(f"Saving failure results with code: {failure_code}")

        try:
            # Find existing router-created record first
            existing_analysis = self.db_session.exec(
                select(RiskyAnalysis).where(
                    RiskyAnalysis.scrape_request_ref_id == self.scrape_request_ref_id,
                    RiskyAnalysis.processing_status.in_(["PENDING", "PROCESSING"])
                )
            ).first()

            if existing_analysis:
                # Update existing record with failure details
                self.logger.info(f"FAILURE HANDLING: Updating existing record ID {existing_analysis.id} with failure details")

                existing_analysis.website = website
                existing_analysis.analysis_flow_used = "failed_both_flows" if flow_attempted == "both" else f"failed_{flow_attempted}_flow"
                existing_analysis.reachability_percentage = reachability_percentage
                existing_analysis.overall_result = json.dumps({
                    "status": "failed",
                    "reason": f"both_normal_and_backup_flows_failed" if flow_attempted == "both" else f"{flow_attempted}_flow_failed",
                    "flow_attempted": flow_attempted,
                    "reachability_percentage": reachability_percentage,
                    "error_message": error_msg
                })
                existing_analysis.processing_status = failure_code
                existing_analysis.error_message = error_msg
                existing_analysis.completed_at = get_current_time()
                existing_analysis.org_id = self.org_id

                analysis_id = existing_analysis.id

                self.logger.info(f"FAILURE HANDLING: Updated existing analysis record {analysis_id} with failure code {failure_code}")

            else:
                # Create new failure record if none exists (fallback)
                self.logger.info(f"FAILURE HANDLING: No existing record found, creating new failure record")

                risky_analysis = RiskyAnalysis(
                    website=website,
                    scrape_request_ref_id=self.scrape_request_ref_id,
                    analysis_flow_used="failed_both_flows" if flow_attempted == "both" else f"failed_{flow_attempted}_flow",
                    reachability_percentage=reachability_percentage,
                    total_urls_processed=0,
                    overall_result=json.dumps({
                        "status": "failed",
                        "reason": f"both_normal_and_backup_flows_failed" if flow_attempted == "both" else f"{flow_attempted}_flow_failed",
                        "flow_attempted": flow_attempted,
                        "reachability_percentage": reachability_percentage,
                        "error_message": error_msg
                    }),
                    is_risky=False,
                    risk_categories="[]",
                    keywords_found="[]",
                    reason_for_risky="[]",
                    analysis_details=json.dumps({
                        "status": "failed",
                        "details": error_msg,
                        "failure_code": failure_code
                    }),
                    processing_status=failure_code,
                    error_message=error_msg,
                    created_at=get_current_time(),
                    started_at=get_current_time(),
                    completed_at=get_current_time(),
                    org_id=self.org_id
                )

                self.db_session.add(risky_analysis)
                self.db_session.flush()

                analysis_id = risky_analysis.id

            # Commit the failure record
            self.db_session.commit()

            self.logger.info(f"FAILURE HANDLING: Saved failure results with analysis_id {analysis_id}")
            return analysis_id

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Error saving failure results: {str(e)}", error=e)
            return None

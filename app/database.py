from sqlmodel import Session, SQLModel, create_engine

from app.config import settings

# Create the database engine with connection pooling and timeout settings
# Optimized for both SQLite and MySQL
connect_args = {}
if "mysql" in settings.DATABASE_URL:
    connect_args = {
        "charset": "utf8mb4",
        "use_unicode": True,
        "autocommit": False
    }

engine = create_engine(
    settings.DATABASE_URL,
    echo=True,
    pool_size=10 if "mysql" in settings.DATABASE_URL else 5,  # Increased for MySQL
    max_overflow=20 if "mysql" in settings.DATABASE_URL else 10,  # Increased for MySQL
    pool_timeout=30,  # Timeout for getting a connection from pool
    pool_recycle=3600,  # Recycle connections after 1 hour
    pool_pre_ping=True,  # Enable connection health checks
    connect_args=connect_args
)


def init_db():
    SQLModel.metadata.create_all(engine)


def get_session():
    with Session(engine) as session:
        yield session

# Alias for get_session to maintain compatibility with different naming conventions
get_db = get_session

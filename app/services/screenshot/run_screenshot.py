#!/usr/bin/env python3
import asyncio
import argparse
import sys
import logging
import json
import os
import base64
import tempfile
import time
from playwright.async_api import async_playwright

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
    stream=sys.stdout
)

logger = logging.getLogger("screenshot")

USERNAME = "<EMAIL>"
PASSWORD = "Biztelsocial@1802"

SESSION_FILE = "instagram_session.json"

async def ensure_instagram_login(context, page, target_url=None):
    """
    Enhanced Instagram login with better error handling and session management

    Args:
        context: Browser context
        page: Page object
        target_url: The original URL we want to access after login
    """
    logger.info("Starting Instagram login process...")

    # Check for existing session
    if os.path.exists("instagram_cookies.json"):
        logger.info("Found existing Instagram session, attempting to use it...")
        try:
            # Load cookies
            with open("instagram_cookies.json", "r") as f:
                cookies = json.load(f)
            await context.add_cookies(cookies)

            # Test the session by going to Instagram home
            await page.goto("https://www.instagram.com/", timeout=30000)
            await page.wait_for_timeout(3000)

            # Verify login by checking for logged-in indicators
            logged_in_selectors = [
                'nav[role="navigation"]',
                '[data-testid="user-avatar"]',
                'svg[aria-label="Home"]',
                'a[href="/direct/inbox/"]'
            ]

            is_logged_in = False
            for selector in logged_in_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=3000)
                    is_logged_in = True
                    logger.info(f"Session verified with selector: {selector}")
                    break
                except:
                    continue

            if is_logged_in:
                logger.info("Existing Instagram session is valid")
                return True
            else:
                logger.warning("Existing session appears invalid, will perform fresh login")

        except Exception as e:
            logger.warning(f"Error using existing session: {str(e)}")

    # Perform fresh login
    logger.info("Performing fresh Instagram login...")
    try:
        await page.goto("https://www.instagram.com/accounts/login/", timeout=30000)
        await page.wait_for_timeout(2000)

        # Wait for login form
        await page.wait_for_selector('input[name="username"]', timeout=10000)

        # Fill credentials
        logger.info("Filling login credentials...")
        await page.fill('input[name="username"]', USERNAME)
        await page.wait_for_timeout(500)
        await page.fill('input[name="password"]', PASSWORD)
        await page.wait_for_timeout(500)

        # Submit login form
        logger.info("Submitting login form...")
        await page.click('button[type="submit"]')

        # Wait for login to complete
        await page.wait_for_timeout(5000)

        # Handle potential 2FA or verification screens
        current_url = page.url
        if "challenge" in current_url or "two_factor" in current_url:
            logger.warning("Instagram requires additional verification (2FA/challenge)")
            logger.warning("Please complete verification manually or use an account without 2FA")
            return False

        # Verify successful login
        try:
            # Wait for navigation away from login page
            await page.wait_for_function(
                "() => !window.location.pathname.includes('/accounts/login')",
                timeout=15000
            )

            # Look for logged-in indicators
            logged_in_selectors = [
                'nav[role="navigation"]',
                'svg[aria-label="Home"]',
                'a[href="/direct/inbox/"]'
            ]

            login_success = False
            for selector in logged_in_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    login_success = True
                    logger.info(f"Login verified with selector: {selector}")
                    break
                except:
                    continue

            if login_success:
                # Save session cookies
                logger.info("Saving Instagram session...")
                cookies = await context.cookies()
                with open("instagram_cookies.json", "w") as f:
                    json.dump(cookies, f)
                logger.info("Instagram login successful and session saved")
                return True
            else:
                logger.error("Login appeared to complete but couldn't verify logged-in state")
                return False

        except Exception as e:
            logger.error(f"Error verifying login completion: {str(e)}")
            return False

    except Exception as e:
        logger.error(f"Instagram login failed: {str(e)}")
        return False

async def ensure_facebook_login(context, page, target_url=None):
    """
    Enhanced Facebook login with better error handling and session management

    Args:
        context: Browser context
        page: Page object
        target_url: The original URL we want to access after login
    """
    logger.info("Starting Facebook login process...")
    cookie_file = "facebook_cookies.json"

    # Check for existing session
    if os.path.exists(cookie_file):
        logger.info("Found existing Facebook session, attempting to use it...")
        try:
            # Load cookies
            with open(cookie_file, "r") as f:
                cookies = json.load(f)
            await context.add_cookies(cookies)

            # Test the session by going to Facebook home
            await page.goto("https://www.facebook.com/", timeout=30000)
            await page.wait_for_timeout(3000)

            # Verify login by checking for logged-in indicators
            logged_in_selectors = [
                'image[alt*="profile picture"]',
                '[aria-label="Your profile"]',
                '[data-testid="blue_bar"]',
                'div[role="banner"]'
            ]

            is_logged_in = False
            for selector in logged_in_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=3000)
                    is_logged_in = True
                    logger.info(f"Facebook session verified with selector: {selector}")
                    break
                except:
                    continue

            if is_logged_in:
                logger.info("Existing Facebook session is valid")
                return True
            else:
                logger.warning("Existing session appears invalid, will perform fresh login")

        except Exception as e:
            logger.warning(f"Error using existing Facebook session: {str(e)}")

    # Perform fresh login
    logger.info("Performing fresh Facebook login...")
    try:
        await page.goto("https://www.facebook.com/login/", timeout=30000)
        await page.wait_for_timeout(2000)

        # Wait for login form
        await page.wait_for_selector('input[name="email"]', timeout=10000)

        # Fill credentials
        logger.info("Filling Facebook login credentials...")
        await page.fill('input[name="email"]', USERNAME)
        await page.wait_for_timeout(500)
        await page.fill('input[name="pass"]', PASSWORD)
        await page.wait_for_timeout(500)

        # Submit login form
        logger.info("Submitting Facebook login form...")
        await page.click('button[name="login"]')

        # Wait for login to complete
        await page.wait_for_timeout(5000)

        # Handle post-login redirect
        current_url = page.url
        if "sk=welcome" in current_url:
            logger.info("Detected Facebook welcome screen, redirecting to homepage...")
            await page.goto("https://www.facebook.com/", timeout=30000)
            await page.wait_for_timeout(3000)

        # Handle potential security checks
        current_url = page.url
        if "checkpoint" in current_url or "two_factor" in current_url:
            logger.warning("Facebook requires additional verification (checkpoint/2FA)")
            logger.warning("Please complete verification manually or use an account without 2FA")
            return False

        # Verify successful login
        try:
            # Wait for navigation away from login page
            await page.wait_for_function(
                "() => !window.location.pathname.includes('/login')",
                timeout=15000
            )

            # Look for logged-in indicators
            logged_in_selectors = [
                'image[alt*="profile picture"]',
                '[aria-label="Your profile"]',
                '[data-testid="blue_bar"]',
                'div[role="banner"]'
            ]

            login_success = False
            for selector in logged_in_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    login_success = True
                    logger.info(f"Facebook login verified with selector: {selector}")
                    break
                except:
                    continue

            if login_success:
                # Save session cookies
                logger.info("Saving Facebook session...")
                cookies = await context.cookies()
                with open(cookie_file, "w") as f:
                    json.dump(cookies, f)
                logger.info("Facebook login successful and session saved")
                return True
            else:
                logger.error("Login appeared to complete but couldn't verify logged-in state")
                return False

        except Exception as e:
            logger.error(f"Error verifying Facebook login completion: {str(e)}")
            return False

    except Exception as e:
        logger.error(f"Facebook login failed: {str(e)}")
        return False





# async def ensure_instagram_login(context, page):
#     """
#     Ensure we are logged in to Instagram and save the session.
#     """
#     logger.info("Performing Instagram login...")

#     await page.goto("https://www.instagram.com/accounts/login/", timeout=60000)
#     await page.wait_for_selector('input[name="username"]')

#     await page.fill('input[name="username"]', USERNAME)
#     await page.fill('input[name="password"]', PASSWORD)
#     await page.click('button[type="submit"]')

#     await page.wait_for_load_state("networkidle")
#     await asyncio.sleep(5)

#     # Verify login success
#     if await page.locator('svg[aria-label="Home"]').count() == 0:
#         raise Exception("Instagram login failed.")

#     logger.info("Instagram login successful. Saving session...")

#     session = await context.storage_state()
#     with open(SESSION_FILE, "w") as f:
#         json.dump(session, f)


async def capture_screenshot(url, timeout=30, close_popups=True, wait_after_close=100):
    """
    Capture a screenshot of a URL using Playwright - OPTIMIZED VERSION
    
    Args:
        url (str): The URL to capture a screenshot of
        timeout (int): Timeout in seconds (reduced from 90 to 30)
        close_popups (bool): Whether to attempt closing popups
        wait_after_close (int): Time in milliseconds to wait after closing popups (reduced from 500 to 100)
        
    Returns:
        bytes: Screenshot data as bytes
    """
    start_time = time.time()
    logger.info(f"Starting OPTIMIZED screenshot capture for {url}")
    
    async with async_playwright() as playwright:
        # Optimized browser launch with performance flags
        browser = await playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox', 
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--window-size=1920,1080',
                '--disable-features=TranslateUI',
                '--disable-renderer-backgrounding',
                '--disable-background-networking'
            ]
        )
        # context = await browser.new_context(viewport={"width": 1920, "height": 1080})
        # page = await context.new_page()
        # Enhanced social media login handling
        context = await browser.new_context(viewport={"width": 1920, "height": 1080})
        page = await context.new_page()

        # Handle Instagram login if needed
        if "instagram.com" in url:
            logger.info("Instagram URL detected, ensuring login...")
            login_success = await ensure_instagram_login(context, page, url)
            if not login_success:
                logger.warning("Instagram login failed, proceeding with screenshot anyway")
            else:
                logger.info("Instagram login successful")

        # Handle Facebook login if needed
        elif "facebook.com" in url:
            logger.info("Facebook URL detected, ensuring login...")
            login_success = await ensure_facebook_login(context, page, url)
            if not login_success:
                logger.warning("Facebook login failed, proceeding with screenshot anyway")
            else:
                logger.info("Facebook login successful")


        
        try:
            # Set faster timeouts for the page
            page.set_default_timeout(timeout * 1000)
            page.set_default_navigation_timeout(timeout * 1000)
            
            # Navigate to the page with optimized wait strategy
            logger.info(f"Navigating to {url}")
            await page.goto(url, wait_until="domcontentloaded", timeout=timeout*1000)  # Changed from networkidle to domcontentloaded
            
            if close_popups:
                logger.info(f"POPUP HANDLING ENABLED for {url} - Will attempt to close popups for better screenshot")
                # Enhanced wait time for popups to load
                await asyncio.sleep(1.5)  # Increased from 0.5 to 1.5 for better popup detection

                # Try to press Escape key as it often closes modal popups on many sites
                try:
                    await page.keyboard.press('Escape')
                    logger.info("Pressed Escape key to try closing popup")
                    await page.wait_for_timeout(500)
                except Exception as e:
                    logger.warning(f"Error pressing Escape key: {str(e)}")
            else:
                logger.info(f"POPUP HANDLING DISABLED for {url} - Preserving popups as they may contain important content")
                
                # Platform-specific popup handling
                if "instagram.com" in url:
                    logger.info("Handling Instagram-specific popups...")
                    
                    try:
                        # Look for login/signup dialog and close button
                        close_button = page.locator('svg[aria-label="Close"][role="img"]')
                        if await close_button.count() > 0:
                            logger.info("Found Instagram popup close button...")
                            button = close_button.locator('xpath=ancestor::button').first
                            await button.click()
                            await page.wait_for_timeout(100)  # Reduced from 300 to 100
                    except Exception as e:
                        logger.warning(f"Error closing Instagram popup: {str(e)}")
                
                elif "facebook.com" in url:
                    logger.info("Handling Facebook-specific popups...")
                    
                    try:
                        # Close cookie consent/login popup
                        cookie_buttons = page.locator('button[data-cookiebanner="accept_button"], '
                                                  'button[data-testid="cookie-policy-manage-dialog-accept-button"]')
                        if await cookie_buttons.count() > 0:
                            await cookie_buttons.first.click()
                            logger.info("Clicked cookie consent button")
                            await page.wait_for_timeout(100)  # Reduced from 300 to 100

                        # Close login dialog
                        login_close = page.locator('[aria-label="Close"]')
                        if await login_close.count() > 0:
                            await login_close.first.click()
                            logger.info("Closed Facebook login dialog")
                            await page.wait_for_timeout(100)  # Reduced from 300 to 100
                    except Exception as e:
                        logger.warning(f"Error handling Facebook popup: {str(e)}")
                
                elif any(x_domain in url for x_domain in ["twitter.com", "x.com"]):
                    logger.info("Handling X/Twitter-specific popups...")
                    
                    try:
                        # Handle the login wall popup
                        login_dialog_close = page.locator('div[aria-modal="true"] div[role="button"][data-testid="close"]')
                        if await login_dialog_close.count() > 0:
                            await login_dialog_close.click()
                            logger.info("Closed X/Twitter login dialog")
                            await page.wait_for_timeout(300)
                            
                        # Handle cookie consent
                        cookie_buttons = page.locator('div[role="dialog"] button:has-text("Accept")')
                        if await cookie_buttons.count() > 0:
                            await cookie_buttons.first.click()
                            logger.info("Clicked X/Twitter cookie accept button")
                            await page.wait_for_timeout(300)
                    except Exception as e:
                        logger.warning(f"Error handling X/Twitter popup: {str(e)}")
                
                elif "linkedin.com" in url:
                    logger.info("Handling LinkedIn-specific popups...")
                    
                    try:
                        # Close signin popup
                        dismiss_buttons = page.locator('button.artdeco-modal__dismiss, button[aria-label="Dismiss"]')
                        if await dismiss_buttons.count() > 0:
                            await dismiss_buttons.first.click()
                            logger.info("Dismissed LinkedIn popup")
                            await page.wait_for_timeout(300)
                            
                        # Cookie consent banner
                        cookie_buttons = page.locator('button[action-type="DENY"], button[data-control-name="ga-cookie.consent.reject.v4"]')
                        if await cookie_buttons.count() > 0:
                            await cookie_buttons.first.click()
                            logger.info("Rejected LinkedIn cookies")
                            await page.wait_for_timeout(300)
                            
                        # Login wall
                        try:
                            # The main page scroll might still work to see content even with login wall
                            await page.mouse.wheel(0, 500)
                            logger.info("Scrolled down to potentially see content behind login wall")
                        except Exception:
                            pass
                    except Exception as e:
                        logger.warning(f"Error handling LinkedIn popup: {str(e)}")
                
                # Generic popup handling for all platforms
                try:
                    # Try to find and handle any common dialog popups that weren't caught by platform-specific handlers
                    
                    # Method 1: Look for elements with role="dialog" and find close buttons within them
                    dialog = page.locator('div[role="dialog"]')
                    if await dialog.count() > 0:
                        logger.info("Found a dialog popup, attempting to close it...")
                        
                        # Try to find close buttons with various common patterns
                        close_buttons = [
                            # SVG close buttons (common in modern sites)
                            'svg[aria-label="Close"], svg[aria-label="close"], svg[aria-label="Dismiss"]',
                            # X text buttons 
                            'button:has-text("✕"), button:has-text("×"), button:has-text("X")',
                            # Close/Cancel text buttons
                            'button:has-text("Close"), button:has-text("Cancel"), button:has-text("Dismiss")',
                            # Aria labeled buttons
                            'button[aria-label="Close"], button[aria-label="Dismiss"], button[aria-label="Cancel"]',
                            # Modal dismiss buttons
                            '.modal-close, .modal-dismiss, .close-button'
                        ]
                        
                        # Try each selector
                        for selector in close_buttons:
                            button = page.locator(selector)
                            if await button.count() > 0:
                                await button.first.click()
                                logger.info(f"Closed dialog with selector: {selector}")
                                await page.wait_for_timeout(wait_after_close)
                                break
                    
                    # Method 2: Try clicking the backdrop/overlay (often closes modals)
                    overlays = page.locator('.modal-backdrop, .modal-overlay, .overlay, .dialog-overlay')
                    if await overlays.count() > 0:
                        try:
                            await overlays.first.click({position: {x: 10, y: 10}})  # Click near the edge
                            logger.info("Clicked on overlay/backdrop to attempt closing popup")
                            await page.wait_for_timeout(wait_after_close)
                        except:
                            pass
                    
                    # Method 3: Try a few more generic approaches if still needed
                    generic_buttons = page.locator('button, a[role="button"]')
                    for i in range(min(await generic_buttons.count(), 5)):  # Check only first few buttons
                        try:
                            button = generic_buttons.nth(i)
                            button_text = await button.text_content() or ""
                            button_aria = await button.get_attribute("aria-label") or ""
                            
                            # Check if this looks like a close button
                            if any(x in button_text.lower() for x in ["close", "dismiss", "cancel", "✕", "×", "x"]) or \
                               any(x in button_aria.lower() for x in ["close", "dismiss", "cancel"]):
                                await button.click()
                                logger.info(f"Clicked potential close button: {button_text or button_aria}")
                                await page.wait_for_timeout(wait_after_close)
                                break
                        except:
                            continue
                            
                except Exception as e:
                    logger.warning(f"Error while trying generic popup handling: {str(e)}")
            
            # Take a screenshot with optimization
            logger.info("Taking optimized full page screenshot")
            screenshot_bytes = await page.screenshot(
                timeout=15000,  # Increased timeout for full page screenshots
                full_page=True,  # RESTORED: Full page screenshots are essential
                type='png'       # Changed back to PNG for better quality with full page
            )
            
            capture_time = time.time() - start_time
            logger.info(f"Screenshot captured in {capture_time:.2f}s, size: {len(screenshot_bytes)} bytes")
            
            return screenshot_bytes
            
        except Exception as e:
            logger.error(f"Error capturing screenshot: {str(e)}")
            return None
        finally:
            await browser.close()

async def main_async(url, output=None, wait_time=500):
    screenshot_data = await capture_screenshot(url, wait_after_close=wait_time)
    
    if screenshot_data:
        if output:
            # Save to specified file
            with open(output, "wb") as f:
                f.write(screenshot_data)
            print(f"Screenshot saved to {output}")
        else:
            # Save with automatic filename
            timestamp = int(time.time())
            domain = url.replace("https://", "").replace("http://", "").split("/")[0]
            filename = f"{domain}_{timestamp}.png"
            
            with open(filename, "wb") as f:
                f.write(screenshot_data)
            print(f"Screenshot saved to {filename}")
        return 0
    else:
        print("Failed to capture screenshot")
        return 1

def main():
    parser = argparse.ArgumentParser(description="Capture a screenshot of a URL")
    parser.add_argument("url", help="The URL to capture (include http:// or https://)")
    parser.add_argument("-o", "--output", help="Output filename (optional)")
    parser.add_argument("-w", "--wait", type=int, default=500, 
                        help="Wait time in milliseconds after closing popups (default: 500)")
    
    args = parser.parse_args()
    
    # Run the async function
    result = asyncio.run(main_async(args.url, args.output, args.wait))
    sys.exit(result)

if __name__ == "__main__":
    main() 
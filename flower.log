nohup: ignoring input
2025-07-19 17:21:42,848 [flower.command] INFO: Visit me at http://0.0.0.0:5555
2025-07-19 17:21:42,853 [flower.command] INFO: Broker: redis://:**@localhost:6379/0
2025-07-19 17:21:42,857 [flower.command] INFO: Registered tasks: 
['celery.accumulate',
 'celery.backend_cleanup',
 'celery.chain',
 'celery.chord',
 'celery.chord_unlock',
 'celery.chunks',
 'celery.group',
 'celery.map',
 'celery.starmap',
 'health_check_mcc',
 'health_check_url_classification',
 'process_mcc_analysis',
 'process_policy_analysis',
 'process_policy_analysis_enhanced',
 'process_risky_classification',
 'process_url_classification',
 'process_url_classification_v2',
 'retry_mcc_analysis',
 'retry_url_classification',
 'test_task']
2025-07-19 17:21:42,868 [kombu.mixins] INFO: Connected to redis://:**@localhost:6379/0

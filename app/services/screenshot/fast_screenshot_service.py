"""
Fast Screenshot Service - Integration wrapper for batch screenshot processing
Provides easy integration with existing policy, MCC, and risky analysis services
"""
import asyncio
import time
from typing import List, Dict, Optional, Set
from app.utils.logger import ConsoleLogger
from app.services.screenshot.batch_screenshot import capture_screenshots_for_analysis, BatchScreenshotProcessor

class FastScreenshotService:
    """
    Service for fast batch screenshot processing with smart URL selection
    """
    
    def __init__(self, scrape_request_ref_id: str, analysis_type: str = "policy"):
        """
        Initialize the screenshot service
        
        Args:
            scrape_request_ref_id (str): Reference ID for tracking
            analysis_type (str): Type of analysis (policy, mcc, risky)
        """
        self.scrape_request_ref_id = scrape_request_ref_id
        self.analysis_type = analysis_type
        self.logger = ConsoleLogger(scrape_request_ref_id)
    
    def select_priority_urls(self, classified_urls: Dict[str, List[str]], 
                           max_urls: int = 10) -> List[str]:
        """
        Select priority URLs for screenshot capture based on category importance
        
        Args:
            classified_urls (Dict[str, List[str]]): URLs organized by category
            max_urls (int): Maximum number of URLs to select
            
        Returns:
            List[str]: Selected priority URLs
        """
        # Priority order for different analysis types
        if self.analysis_type == "policy":
            priority_categories = [
                "home_page", "about_us", "privacy_policy", "terms_and_condition",
                "returns_cancellation_exchange", "shipping_delivery", "contact_us"
            ]
        elif self.analysis_type == "mcc":
            priority_categories = [
                "home_page", "about_us", "catalogue", "products", "services"
            ]
        else:  # risky analysis
            priority_categories = [
                "home_page", "about_us", "products", "services", "contact_us"
            ]
        
        selected_urls = []
        urls_seen = set()
        
        # First pass: Get high priority categories
        for category in priority_categories:
            if category in classified_urls:
                for url in classified_urls[category]:
                    if url not in urls_seen and len(selected_urls) < max_urls:
                        selected_urls.append(url)
                        urls_seen.add(url)
        
        # Second pass: Fill remaining slots with other categories
        if len(selected_urls) < max_urls:
            for category, urls in classified_urls.items():
                if category not in priority_categories:
                    for url in urls:
                        if url not in urls_seen and len(selected_urls) < max_urls:
                            selected_urls.append(url)
                            urls_seen.add(url)
        
        self.logger.info(f"Selected {len(selected_urls)} priority URLs for screenshots", {
            "analysis_type": self.analysis_type,
            "max_urls": max_urls,
            "total_available": sum(len(urls) for urls in classified_urls.values())
        })
        
        return selected_urls
    
    async def capture_priority_screenshots(self, classified_urls: Dict[str, List[str]],
                                         max_urls: int = 10) -> Dict[str, str]:
        """
        Capture screenshots for priority URLs with conditional popup handling

        Args:
            classified_urls (Dict[str, List[str]]): URLs organized by category
            max_urls (int): Maximum number of URLs to capture

        Returns:
            Dict[str, str]: Mapping of URL to Azure URL
        """
        start_time = time.time()

        # Select priority URLs
        priority_urls = self.select_priority_urls(classified_urls, max_urls)

        if not priority_urls:
            self.logger.warning("No priority URLs found for screenshot capture")
            return {}

        # Create enhanced popup settings based on URL type
        url_popup_settings = {}
        # Enhanced social media categories - include platforms that have problematic popups
        social_media_categories = {
            "instagram_page", "facebook_page", "twitter_page", "pinterest_page", "x_page",
            "linkedin_page", "youtube_page", "tiktok_page", "snapchat_page"
            # Added back: "linkedin_page", "youtube_page" - they do have popups that can block content
            # Added: "tiktok_page", "snapchat_page" for comprehensive coverage
        }
        url_to_category = {url: category for category, urls in classified_urls.items() for url in urls}

        for url in priority_urls:
            category = url_to_category.get(url)
            is_social_media = category in social_media_categories

            # Enhanced logic: For social media URLs close_popup=True, for non-social media close_popup=False
            # This ensures we preserve popups on business websites while closing them on social media
            url_popup_settings[url] = is_social_media

            self.logger.info(f"Enhanced popup setting: {url} -> close_popups={is_social_media}", {
                "category": category,
                "is_social_media": is_social_media,
                "url_domain": url.split('/')[2] if len(url.split('/')) > 2 else url
            })

        self.logger.info(f"Starting priority screenshot capture", {
            "url_count": len(priority_urls),
            "analysis_type": self.analysis_type,
            "social_media_urls": sum(1 for url in priority_urls if url_to_category.get(url) in social_media_categories),
            "non_social_media_urls": sum(1 for url in priority_urls if url_to_category.get(url) not in social_media_categories)
        })

        # Capture screenshots using batch processor with conditional popup handling
        azure_urls = await capture_screenshots_for_analysis(
            priority_urls,
            self.scrape_request_ref_id,
            self.analysis_type,
            url_popup_settings
        )

        processing_time = time.time() - start_time
        success_count = len(azure_urls)

        self.logger.info(f"Priority screenshot capture completed", {
            "total_urls": len(priority_urls),
            "successful_captures": success_count,
            "success_rate": f"{(success_count/len(priority_urls)*100):.1f}%",
            "processing_time": f"{processing_time:.2f}s"
        })

        return azure_urls
    
    async def capture_specific_screenshots(self, urls: List[str], classified_urls: Dict[str, List[str]] = None) -> Dict[str, str]:
        """
        Capture screenshots for specific URLs with conditional popup handling

        Args:
            urls (List[str]): Specific URLs to capture
            classified_urls (Dict[str, List[str]], optional): Classified URLs to determine social media status.

        Returns:
            Dict[str, str]: Mapping of URL to Azure URL
        """
        if not urls:
            return {}

        # Create popup settings based on URL type
        url_popup_settings = {}
        if classified_urls:
            # Enhanced social media categories - include platforms that have problematic popups
            social_media_categories = {
                "instagram_page", "facebook_page", "twitter_page", "pinterest_page", "x_page",
                "linkedin_page", "youtube_page", "tiktok_page", "snapchat_page"
                # Added back: "linkedin_page", "youtube_page" - they do have popups that can block content
                # Added: "tiktok_page", "snapchat_page" for comprehensive coverage
            }
            url_to_category = {url: category for category, urls in classified_urls.items() for url in urls}
            for url in urls:
                category = url_to_category.get(url)
                is_social_media = category in social_media_categories
                # For social media URLs: close_popup=True, for non-social media: close_popup=False
                url_popup_settings[url] = is_social_media
        else:
            # Enhanced fallback to keyword matching if no classification is provided
            # Include comprehensive social media patterns that have problematic popups
            social_media_patterns = [
                "instagram.com", "facebook.com", "fb.com", "twitter.com",
                "x.com", "pinterest.com", "pin.it", "linkedin.com",
                "youtube.com", "youtu.be", "tiktok.com", "snapchat.com"
                # Added back: "linkedin.com", "youtube.com", "youtu.be" - they do have popups
                # Added: "tiktok.com", "snapchat.com" for comprehensive coverage
            ]
            for url in urls:
                is_social_media = any(pattern in url.lower() for pattern in social_media_patterns)
                url_popup_settings[url] = is_social_media

        self.logger.info(f"Starting specific screenshot capture for {len(urls)} URLs")

        azure_urls = await capture_screenshots_for_analysis(
            urls,
            self.scrape_request_ref_id,
            self.analysis_type,
            url_popup_settings
        )

        return azure_urls
    
    def format_screenshot_results(self, azure_urls: Dict[str, str], 
                                classified_urls: Dict[str, List[str]]) -> Dict[str, any]:
        """
        Format screenshot results for analysis services
        
        Args:
            azure_urls (Dict[str, str]): URL to Azure URL mapping
            classified_urls (Dict[str, List[str]]): Original classified URLs
            
        Returns:
            Dict: Formatted results with metadata
        """
        # Group screenshots by category
        screenshots_by_category = {}
        for category, urls in classified_urls.items():
            category_screenshots = {}
            for url in urls:
                if url in azure_urls:
                    category_screenshots[url] = azure_urls[url]
            if category_screenshots:
                screenshots_by_category[category] = category_screenshots
        
        # Create summary
        total_available_urls = sum(len(urls) for urls in classified_urls.values())
        total_screenshots = len(azure_urls)
        
        result = {
            "screenshots_by_category": screenshots_by_category,
            "all_screenshots": azure_urls,
            "summary": {
                "total_urls_available": total_available_urls,
                "screenshots_captured": total_screenshots,
                "categories_with_screenshots": len(screenshots_by_category),
                "coverage_percentage": (total_screenshots / total_available_urls * 100) if total_available_urls > 0 else 0
            },
            "metadata": {
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "analysis_type": self.analysis_type,
                "capture_timestamp": time.time()
            }
        }
        
        return result

# Convenience functions for easy integration with existing services

async def capture_policy_screenshots(classified_urls: Dict[str, List[str]],
                                   scrape_request_ref_id: str,
                                   max_screenshots: int = 10) -> Dict[str, any]:
    """
    Convenience function for policy analysis screenshot capture with conditional popup handling

    Args:
        classified_urls (Dict[str, List[str]]): Classified URLs from policy analysis
        scrape_request_ref_id (str): Reference ID
        max_screenshots (int): Maximum screenshots to capture

    Returns:
        Dict: Formatted screenshot results
    """
    service = FastScreenshotService(scrape_request_ref_id, "policy")
    azure_urls = await service.capture_priority_screenshots(classified_urls, max_screenshots)
    return service.format_screenshot_results(azure_urls, classified_urls)

async def capture_mcc_screenshots(classified_urls: Dict[str, List[str]], 
                                scrape_request_ref_id: str, 
                                max_screenshots: int = 8) -> Dict[str, any]:
    """
    Convenience function for MCC analysis screenshot capture
    
    Args:
        classified_urls (Dict[str, List[str]]): Classified URLs from MCC analysis
        scrape_request_ref_id (str): Reference ID
        max_screenshots (int): Maximum screenshots to capture
        
    Returns:
        Dict: Formatted screenshot results
    """
    service = FastScreenshotService(scrape_request_ref_id, "mcc")
    azure_urls = await service.capture_priority_screenshots(classified_urls, max_screenshots)
    return service.format_screenshot_results(azure_urls, classified_urls)

async def capture_risky_screenshots(classified_urls: Dict[str, List[str]], 
                                  scrape_request_ref_id: str, 
                                  max_screenshots: int = 10) -> Dict[str, any]:
    """
    Convenience function for risky analysis screenshot capture
    
    Args:
        classified_urls (Dict[str, List[str]]): Classified URLs from risky analysis
        scrape_request_ref_id (str): Reference ID
        max_screenshots (int): Maximum screenshots to capture
        
    Returns:
        Dict: Formatted screenshot results
    """
    service = FastScreenshotService(scrape_request_ref_id, "risky")
    azure_urls = await service.capture_priority_screenshots(classified_urls, max_screenshots)
    return service.format_screenshot_results(azure_urls, classified_urls)

# Performance testing function
async def test_batch_performance(urls: List[str], max_concurrent: int = 10) -> Dict:
    """
    Test batch screenshot performance
    
    Args:
        urls (List[str]): URLs to test
        max_concurrent (int): Maximum concurrent screenshots
        
    Returns:
        Dict: Performance metrics
    """
    processor = BatchScreenshotProcessor(
        max_concurrent=max_concurrent,
        timeout=25,
        upload_to_azure=False  # Skip Azure for testing
    )
    
    start_time = time.time()
    results = await processor.process_batch(urls)
    total_time = time.time() - start_time
    
    summary = processor.get_summary()
    summary.update({
        "total_batch_time": total_time,
        "urls_per_second": len(urls) / total_time if total_time > 0 else 0,
        "parallel_efficiency": (summary["average_processing_time"] * len(urls)) / total_time if total_time > 0 else 0
    })
    
    return summary 
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
import json
import traceback
import logging

from app.database import get_session
from app.models.db_models import RiskyAnalysis, WebsiteUrls, ScrapeRequestTracker, get_current_time
from app.models.request_models import RiskyUrlRequest, RiskyClassificationPatchRequest
from app.tasks.celery_tasks import process_risky_classification
from app.utils.website_url_processor import store_urls_from_request, get_urls_by_scrape_ref

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/")
def create_or_get_risky_classification(
    request: RiskyUrlRequest, session: Session = Depends(get_session)
):
    """
    Create a new risky classification task or get existing one with comprehensive error handling
    
    This endpoint accepts website URLs and analyzes them for potentially risky content using
    a dual flow architecture:
    - Normal flow: ≥50% reachable URLs using batches of 10 for Gemini classification
    - Backup flow: <50% reachable using soft classification with text extraction from 6 pages maximum
    
    Both flows stop immediately when risky content is found.
    """
    try:
        logger.info(f"Received risky classification request for website: {request.website}")
        
        # Validate request
        if not request.website or not request.scrapeRequestRefID:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Website and scrapeRequestRefID are required"
            )
        
        if not request.parsed_urls or len(request.parsed_urls) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one URL depth with URLs is required"
            )
        
        # Check for existing analysis
        existing_analysis = session.exec(
            select(RiskyAnalysis).where(
                RiskyAnalysis.scrape_request_ref_id == request.scrapeRequestRefID
            )
        ).first()
        
        if existing_analysis:
            logger.info(f"Found existing risky analysis for ref_id: {request.scrapeRequestRefID}")
            
            # Check if analysis is still processing
            if existing_analysis.processing_status in ["PENDING", "PROCESSING"]:
                return {
                    "message": "Risky classification analysis already in progress",
                    "analysis_id": existing_analysis.id,
                    "scrape_request_ref_id": request.scrapeRequestRefID,
                    "status": existing_analysis.processing_status,
                    "created_at": existing_analysis.created_at
                }
            
            # Return existing completed analysis
            return {
                "message": "Risky classification analysis already completed",
                "analysis_id": existing_analysis.id,
                "scrape_request_ref_id": request.scrapeRequestRefID,
                "status": existing_analysis.processing_status,
                "is_risky": existing_analysis.is_risky,
                "risk_categories": json.loads(existing_analysis.risk_categories) if existing_analysis.risk_categories else [],
                "keywords_found": json.loads(existing_analysis.keywords_found) if existing_analysis.keywords_found else [],
                "reason_for_risky": json.loads(existing_analysis.reason_for_risky) if existing_analysis.reason_for_risky else [],
                "created_at": existing_analysis.created_at,
                "completed_at": existing_analysis.completed_at
            }
        
        # Store URLs in database
        try:
            store_urls_from_request(request, session)
            logger.info(f"URLs stored successfully for ref_id: {request.scrapeRequestRefID}")
        except Exception as e:
            logger.error(f"Error storing URLs: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error storing URLs: {str(e)}"
            )
        
        # DUPLICATION MONITORING: Check record count before creation
        pre_creation_count = session.exec(
            select(RiskyAnalysis).where(
                RiskyAnalysis.scrape_request_ref_id == request.scrapeRequestRefID
            )
        ).all()

        logger.info(f"DUPLICATION MONITORING: Records before creation for ref_id {request.scrapeRequestRefID}: {len(pre_creation_count)}")

        # Create new risky analysis record
        new_analysis = RiskyAnalysis(
            website=request.website,
            scrape_request_ref_id=request.scrapeRequestRefID,
            processing_status="PENDING",
            created_at=get_current_time(),
            org_id=request.org_id or "default"
        )

        session.add(new_analysis)
        session.commit()
        session.refresh(new_analysis)

        logger.info(f"DUPLICATION MONITORING: Created new risky analysis record with ID: {new_analysis.id} for ref_id: {request.scrapeRequestRefID}")

        # DUPLICATION MONITORING: Check record count after creation
        post_creation_count = session.exec(
            select(RiskyAnalysis).where(
                RiskyAnalysis.scrape_request_ref_id == request.scrapeRequestRefID
            )
        ).all()

        logger.info(f"DUPLICATION MONITORING: Records after creation for ref_id {request.scrapeRequestRefID}: {len(post_creation_count)}")

        if len(post_creation_count) > 1:
            logger.warning(f"DUPLICATION DETECTED: Multiple records found for ref_id {request.scrapeRequestRefID}")
            for record in post_creation_count:
                logger.warning(f"  - Record ID {record.id}: status={record.processing_status}, flow={record.analysis_flow_used}, created={record.created_at}")
        
        # Create or update scrape request tracker
        existing_tracker = session.exec(
            select(ScrapeRequestTracker).where(
                ScrapeRequestTracker.scrape_request_ref_id == request.scrapeRequestRefID,
                ScrapeRequestTracker.analysis_type == "risky"
            )
        ).first()
        
        if not existing_tracker:
            tracker = ScrapeRequestTracker(
                scrape_request_ref_id=request.scrapeRequestRefID,
                analysis_type="risky",
                website=request.website,
                analysis_id=new_analysis.id,
                status="PENDING",
                created_at=get_current_time(),
                org_id=request.org_id or "default"
            )
            session.add(tracker)
            session.commit()
            logger.info(f"Created scrape request tracker for ref_id: {request.scrapeRequestRefID}")
        else:
            existing_tracker.analysis_id = new_analysis.id
            existing_tracker.status = "PENDING"
            existing_tracker.created_at = get_current_time()
            session.commit()
            logger.info(f"Updated existing scrape request tracker for ref_id: {request.scrapeRequestRefID}")
        
        # Start Celery task for risky classification processing
        try:
            task = process_risky_classification.delay(new_analysis.id)
            logger.info(f"Started risky classification Celery task: {task.id} for analysis_id: {new_analysis.id}")
        except Exception as e:
            logger.error(f"Error starting Celery task: {str(e)}")
            # Update analysis status to failed
            new_analysis.processing_status = "FAILED"
            new_analysis.error_message = f"Failed to start processing task: {str(e)}"
            session.commit()
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error starting risky classification task: {str(e)}"
            )
        
        return {
            "message": "Risky classification analysis started successfully",
            "analysis_id": new_analysis.id,
            "scrape_request_ref_id": request.scrapeRequestRefID,
            "status": "PENDING",
            "task_id": task.id,
            "created_at": new_analysis.created_at
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        error_msg = f"Unexpected error in risky classification endpoint: {str(e)}"
        logger.error(error_msg)
        logger.error("Full traceback:", exc_info=True)
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.get("/status/{scrape_request_ref_id}")
def get_risky_classification_status(
    scrape_request_ref_id: str,
    org_id: str = "default",
    session: Session = Depends(get_session)
):
    """
    Get the status of a risky classification analysis request
    """
    try:
        logger.info(f"Getting status for risky classification ref_id: {scrape_request_ref_id}")
        
        # Find analysis by scrape request ref ID
        analysis = session.exec(
            select(RiskyAnalysis).where(
                RiskyAnalysis.scrape_request_ref_id == scrape_request_ref_id,
                RiskyAnalysis.org_id == org_id
            )
        ).first()
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No risky classification analysis found for reference ID: {scrape_request_ref_id}"
            )
        
        # Prepare response based on status
        response_data = {
            "analysis_id": analysis.id,
            "scrape_request_ref_id": scrape_request_ref_id,
            "website": analysis.website,
            "status": analysis.processing_status,
            "created_at": analysis.created_at,
            "started_at": analysis.started_at,
            "completed_at": analysis.completed_at,
            "org_id": analysis.org_id
        }
        
        # Add analysis results if completed
        if analysis.processing_status == "COMPLETED":
            response_data.update({
                "is_risky": analysis.is_risky,
                "risk_categories": json.loads(analysis.risk_categories) if analysis.risk_categories else [],
                "keywords_found": json.loads(analysis.keywords_found) if analysis.keywords_found else [],
                "reason_for_risky": json.loads(analysis.reason_for_risky) if analysis.reason_for_risky else [],
                "analysis_flow_used": analysis.analysis_flow_used,
                "reachability_percentage": analysis.reachability_percentage,
                "total_urls_processed": analysis.total_urls_processed
            })
        
        # Add error information if failed
        if analysis.processing_status == "FAILED" and analysis.error_message:
            response_data["error_message"] = analysis.error_message
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Error fetching risky classification status: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.get("/results/{analysis_id}")
def get_risky_classification_results(
    analysis_id: int,
    org_id: str = "default",
    session: Session = Depends(get_session)
):
    """
    Get detailed results of a completed risky classification analysis
    """
    try:
        logger.info(f"Getting detailed results for risky classification analysis_id: {analysis_id}")
        
        # Find analysis by ID
        analysis = session.exec(
            select(RiskyAnalysis).where(
                RiskyAnalysis.id == analysis_id,
                RiskyAnalysis.org_id == org_id
            )
        ).first()
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No risky classification analysis found with ID: {analysis_id}"
            )
        
        if analysis.processing_status != "COMPLETED":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Analysis is not completed. Current status: {analysis.processing_status}"
            )
        
        # Parse analysis details
        analysis_details = {}
        if analysis.analysis_details:
            try:
                analysis_details = json.loads(analysis.analysis_details)
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse analysis_details for analysis_id: {analysis_id}")
        
        return {
            "analysis_id": analysis.id,
            "scrape_request_ref_id": analysis.scrape_request_ref_id,
            "website": analysis.website,
            "status": analysis.processing_status,
            "is_risky": analysis.is_risky,
            "risk_categories": json.loads(analysis.risk_categories) if analysis.risk_categories else [],
            "analysis_flow_used": analysis.analysis_flow_used,
            "reachability_percentage": analysis.reachability_percentage,
            "total_urls_processed": analysis.total_urls_processed,
            "analysis_details": analysis_details,
            "created_at": analysis.created_at,
            "started_at": analysis.started_at,
            "completed_at": analysis.completed_at,
            "org_id": analysis.org_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Error fetching risky classification results: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.patch("/")
def update_risky_classification_webhook(
    request: RiskyClassificationPatchRequest,
    session: Session = Depends(get_session)
):
    """
    PATCH endpoint for risky classification webhook updates

    This endpoint receives webhook data with risky classification results including:
    - website: Website domain
    - scrapeRequestRefID: Scrape request reference ID
    - org_id: Organization ID
    - urls: Array of URLs analyzed
    - risky: Whether content is risky (yes/no)
    - category: Array of risk categories identified
    - keywords_found: Array of risky keywords found
    - reason_for_risky: Array of reasons why content is considered risky

    This follows the same pattern as the policy analysis PATCH endpoint.
    """
    try:
        logger.info(
            "Risky classification PATCH webhook received",
            extra={
                "website": request.website,
                "scrapeRequestRefID": request.scrapeRequestRefID,
                "org_id": request.org_id,
                "url_count": len(request.urls),
                "risky": request.risky,
                "category_count": len(request.category),
                "keywords_count": len(request.keywords_found),
                "reasons_count": len(request.reason_for_risky)
            }
        )

        # Log the webhook data for debugging
        logger.info(
            "Risky classification webhook details",
            extra={
                "website": request.website,
                "scrapeRequestRefID": request.scrapeRequestRefID,
                "org_id": request.org_id,
                "urls": request.urls,
                "risky_status": request.risky,
                "categories": request.category,
                "keywords_found": request.keywords_found,
                "reasons": request.reason_for_risky
            }
        )

        return {
            "message": "Risky classification webhook received successfully",
            "website": request.website,
            "scrapeRequestRefID": request.scrapeRequestRefID,
            "org_id": request.org_id,
            "urls_processed": len(request.urls),
            "risky_status": request.risky,
            "categories_identified": len(request.category),
            "keywords_found": len(request.keywords_found),
            "reasons_provided": len(request.reason_for_risky),
            "status": "COMPLETED"
        }

    except Exception as e:
        error_msg = f"Error processing risky classification webhook: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )

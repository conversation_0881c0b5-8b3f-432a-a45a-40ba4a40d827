import os
import time
import traceback
import asyncio
from datetime import datetime

import google.cloud.storage as storage
from azure.storage.blob.aio import BlobServiceClient
from azure.core.exceptions import AzureError

from app.utils.logger import ConsoleLogger
from app.services.screenshot.url_utils import capture_screenshot_playwright

from dotenv import load_dotenv

load_dotenv()

BUCKET_NAME = os.getenv("BUCKET_NAME")
AZURE_CONNECTION_STRING = os.getenv("AZURE_CONNECTION_STRING")
AZURE_CONTAINER_NAME = os.getenv("AZURE_CONTAINER_NAME")
AZURE_UPLOAD_TIMEOUT = 30  # seconds


def upload_to_gcp(local_file_path, filename):
    storage_client = storage.Client()
    bucket = storage_client.bucket(BUCKET_NAME)
    blob = bucket.blob(filename)
    blob.upload_from_filename(local_file_path)
    blob.make_public()
    gcp_url = blob.public_url
    return gcp_url


async def upload_to_azure_container(local_file_path, filename, logger: ConsoleLogger):
    timestamp = int(time.time())
    filename_with_timestamp = f"{timestamp}_{filename}"

    logger.debug("Generated timestamped filename", {"filename": filename_with_timestamp})

    try:
        # Use context manager for proper cleanup
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_CONNECTION_STRING)
        container_client = blob_service_client.get_container_client(AZURE_CONTAINER_NAME)
        blob_client = container_client.get_blob_client(filename_with_timestamp)

        with open(local_file_path, "rb") as data:
            # Add timeout to upload operation
            await asyncio.wait_for(
                blob_client.upload_blob(data, overwrite=True),
                timeout=AZURE_UPLOAD_TIMEOUT
            )

        # Generate the URL
        azure_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{AZURE_CONTAINER_NAME}/{filename_with_timestamp}"
        logger.debug("Generated Azure URL", {"url": azure_url})
        
        # Close the client connection
        await blob_service_client.close()
        
        return azure_url
        
    except asyncio.TimeoutError:
        logger.error("Azure upload timed out", {"filename": filename_with_timestamp, "timeout": AZURE_UPLOAD_TIMEOUT})
        raise
    except AzureError as e:
        logger.error("Azure upload error", {"error": str(e), "filename": filename_with_timestamp})
        raise
    except Exception as e:
        logger.error("Unexpected error during Azure upload", {"error": str(e), "filename": filename_with_timestamp})
        raise


async def capture_and_upload_screenshot(url, logger: ConsoleLogger):
    try:
        # Pass the logger to the capture_screenshot_playwright function
        screenshot_result = await capture_screenshot_playwright(url)
        
        if not screenshot_result:
            logger.error("Screenshot capture failed - no result returned", {"url": url})
            return ""
        
        # Generate a unique filename for this screenshot
        timestamp = int(time.time())
        domain = url.replace("https://", "").replace("http://", "").split("/")[0]
        filename = f"{domain}_{timestamp}.png"
        
        # Save the data URL to a temporary file
        import tempfile
        import base64
        
        # Extract the base64 data from the data URL
        if isinstance(screenshot_result, str) and screenshot_result.startswith("data:image/png;base64,"):
            base64_data = screenshot_result.replace("data:image/png;base64,", "")
            screenshot_bytes = base64.b64decode(base64_data)
            
            # Create a temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
                temp_file.write(screenshot_bytes)
                local_file_path = temp_file.name
                
            logger.debug("Screenshot saved to temporary file", {"path": local_file_path, "filename": filename})
            
            try:
                azure_url = await upload_to_azure_container(local_file_path, filename, logger)
                logger.debug("Screenshot uploaded", {"url": azure_url})
                
                # Clean up local file
                try:
                    if os.path.exists(local_file_path):
                        os.remove(local_file_path)
                except Exception as clean_error:
                    logger.warning("Failed to clean up local file", {"error": str(clean_error), "path": local_file_path})
                    
                return azure_url
                
            except Exception as e:
                logger.error("Failed to upload screenshot", {"error": str(e), "url": url})
                return ""
        else:
            logger.error("Invalid screenshot format", {"url": url, "type": type(screenshot_result).__name__})
            return ""
    
    except Exception as e:
        logger.error("Screenshot capture and upload failed", {"error": str(e), "url": url})
        return ""

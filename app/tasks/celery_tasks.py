"""
Celery tasks for MCC analysis processing
"""

import os
import time
import json
import asyncio
import traceback
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.celery_app import celery_app
from app.utils.logger import ConsoleLogger
from app.services.mcc_service import MccClassificationService
from app.services.risky_classification_service import RiskyClassificationService
from app.services.policy_analysis_enhanced_service import PolicyAnalysisEnhancedService
from app.models.db_models import RiskyAnalysis, engine
from app.services.risky_classification_service import RiskyClassificationService
from app.utils.webhook_utils import send_risky_classification_webhook
from sqlmodel import Session, select
from datetime import datetime

def _truncate_text_to_words(text: str, max_words: int) -> str:
    """
    Truncate text to a maximum number of words for API compatibility

    Args:
        text (str): Original text to truncate
        max_words (int): Maximum number of words to keep

    Returns:
        str: Truncated text or original if within limit
    """
    if not text or text == "insufficient data":
        return text

    words = text.split()
    if len(words) <= max_words:
        return text

    truncated = " ".join(words[:max_words])
    return truncated

# Policy analysis tasks for the new unified Policy Analysis Service

@celery_app.task(bind=True, name="process_mcc_analysis")
def process_mcc_analysis(self, analysis_id: int):
    """
    Celery task to process MCC analysis
    
    Args:
        analysis_id (int): The ID of the MccAnalysis record to process
    
    Returns:
        dict: Result of the analysis with status and details
    """
    
    logger = ConsoleLogger(analysis_id)
    logger.info(f"Starting MCC analysis task for analysis_id: {analysis_id}")
    
    start_time = time.time()
    
    try:
        # Get the MccAnalysis record from the database
        from sqlmodel import Session, select
        from app.models.db_models import MccAnalysis
        from app.database import engine
        
        logger.info(f"Importing required modules for MCC analysis")
        
        # Import the service here to avoid circular import
        from app.services.mcc_service import MccClassificationService
        
        logger.info(f"Opening database session to get MccAnalysis record")
        with Session(engine) as session:
            # Get the MccAnalysis record
            analysis = session.get(MccAnalysis, analysis_id)
            if not analysis:
                error_msg = f"MccAnalysis with ID {analysis_id} not found"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # Extract values while object is still attached to session
            scrape_request_ref_id = analysis.scrape_request_ref_id
            org_id = analysis.org_id
            website = analysis.website
            
            logger.info(f"Found MccAnalysis record: {website}, ref_id: {scrape_request_ref_id}")
            
            # Update analysis status
            analysis.processing_status = "PROCESSING"
            analysis.started_at = datetime.now().isoformat() + "Z"
            session.commit()
            logger.info(f"Updated MccAnalysis status to PROCESSING")
        
        # Initialize the MCC classification service using extracted values
        logger.info(f"Initializing MCC classification service")
        mcc_service = MccClassificationService(
            scrape_request_ref_id=scrape_request_ref_id,
            org_id=org_id
        )
        
        # Process the MCC analysis
        logger.info(f"Running MCC analysis")
        result = mcc_service.process_mcc_analysis()
        
        processing_time = time.time() - start_time
        logger.info(f"MCC analysis task completed in {processing_time:.2f} seconds with status: {result.get('status')}")
        
        # Update the analysis status with comprehensive error handling
        try:
            logger.info(f"Updating MccAnalysis status to {result.get('status', 'UNKNOWN')}")
            with Session(engine) as session:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    # Safely update status
                    analysis.processing_status = "COMPLETED" if result.get("status") == "COMPLETED" else "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                    
                    # Safely extract and update MCC information
                    if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                        mcc_code = result["mcc_info"].get("mcc")
                        if mcc_code is not None:
                            analysis.mcc_code = str(mcc_code) if not isinstance(mcc_code, str) else mcc_code

                        reasoning = result["mcc_info"].get("reason")
                        if reasoning:
                            analysis.reasoning = str(reasoning)

                        # FIXED: Update business category from MCC info
                        business_category = result["mcc_info"].get("business_category")
                        if business_category:
                            analysis.business_category = str(business_category)

                    # Safely extract and update business description
                    if result.get("website_info") and isinstance(result["website_info"], dict):
                        business_desc = result["website_info"].get("business_description") or result["website_info"].get("website_description")
                        if business_desc:
                            analysis.business_description = str(business_desc)
                    
                    session.commit()
                    logger.info(f"Successfully updated MccAnalysis status")
                else:
                    logger.warning(f"MccAnalysis with ID {analysis_id} not found for status update")
        except Exception as update_error:
            logger.error(f"Error updating MccAnalysis status: {str(update_error)}")
            # Don't fail the task if status update fails - the analysis was completed
        
        # 🚀 NEW: Send MCC results webhook after successful analysis
        if result.get("status") == "COMPLETED":
            try:
                logger.info("📤 Preparing to send MCC results webhook")
                
                # Import webhook function
                from app.utils.webhook_utils import send_mcc_results_webhook
                
                # Prepare webhook data from analysis results
                mcc_webhook_data = {
                    "status": "COMPLETED"
                }
                
                # Extract MCC information
                if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                    mcc_webhook_data["mcc"] = result["mcc_info"].get("mcc", -1)
                    mcc_webhook_data["reason"] = result["mcc_info"].get("reason", "")
                
                # Extract business information
                if result.get("website_info") and isinstance(result["website_info"], dict):
                    business_desc = result["website_info"].get("business_description") or result["website_info"].get("website_description")
                    mcc_webhook_data["businessDescription"] = business_desc or ""
                
                # Get business category from MCC info - FIXED: use correct field name
                business_category = ""
                if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                    business_category = result["mcc_info"].get("business_category", "")
                mcc_webhook_data["businessCategory"] = business_category
                
                # Send the webhook
                webhook_success = send_mcc_results_webhook(
                    scrape_request_ref_id=scrape_request_ref_id,
                    website=website,
                    mcc_result=mcc_webhook_data,
                    analysis_id=analysis_id,
                    logger=logger
                )
                
                if webhook_success:
                    logger.info("✅ MCC results webhook sent successfully")
                else:
                    logger.error("❌ Failed to send MCC results webhook")
                    
            except Exception as webhook_error:
                logger.error(f"❌ Error sending MCC results webhook: {str(webhook_error)}")
                # Don't fail the task if webhook fails - the analysis was completed
        else:
            logger.info("⚠️ Skipping webhook send due to non-COMPLETED status", {"status": result.get("status")})
        
        # Add task execution details to result
        result.update({
            "task_id": self.request.id,
            "analysis_id": analysis_id,
            "execution_time": processing_time
        })
        
        return result
        
    except Exception as e:
        error_msg = f"Error in MCC analysis task: {str(e)}"
        logger.error(error_msg, error=e)
        
        # Log the full traceback for debugging
        logger.error("Full traceback:", error=traceback.format_exc())
        
        processing_time = time.time() - start_time
        
        # Update the analysis status
        try:
            logger.info(f"Updating MccAnalysis status to FAILED after error")
            with Session(engine) as session:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    analysis.processing_status = "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                    analysis.error_message = error_msg
                    session.commit()
                    logger.info(f"Successfully updated MccAnalysis status to FAILED")
                    
                    # Extract website for webhook
                    website = getattr(analysis, 'website', '')
                    scrape_request_ref_id = getattr(analysis, 'scrape_request_ref_id', '')
                    
        except Exception as db_error:
            logger.error(f"Error updating analysis status: {str(db_error)}")
            website = ''
            scrape_request_ref_id = ''
        
        # 🚀 NEW: Send FAILED status webhook 
        if website and scrape_request_ref_id:
            try:
                logger.info("📤 Preparing to send FAILED status webhook")
                from app.utils.webhook_utils import send_mcc_results_webhook
                
                failed_webhook_data = {
                    "status": "FAILED",
                    "mcc": -1,
                    "businessCategory": "",
                    "businessDescription": "",
                    "error": error_msg
                }
                
                webhook_success = send_mcc_results_webhook(
                    scrape_request_ref_id=scrape_request_ref_id,
                    website=website,
                    mcc_result=failed_webhook_data,
                    analysis_id=analysis_id,
                    logger=logger
                )
                
                if webhook_success:
                    logger.info("✅ FAILED status webhook sent successfully")
                else:
                    logger.error("❌ Failed to send FAILED status webhook")
                    
            except Exception as webhook_error:
                logger.error(f"❌ Error sending FAILED status webhook: {str(webhook_error)}")
        
        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "analysis_id": analysis_id,
            "execution_time": processing_time
        }


@celery_app.task(bind=True, name="retry_mcc_analysis")
def retry_mcc_analysis(self, analysis_id: int, max_retries: int = 3):
    """
    Celery task to retry MCC analysis with automatic retry logic
    
    Args:
        analysis_id (int): The ID of the MccAnalysis record to process
        max_retries (int): Maximum number of retries
    
    Returns:
        dict: Result of the analysis with status and details
    """
    
    logger = ConsoleLogger(analysis_id)
    
    try:
        # Use Celery's built-in retry mechanism
        result = process_mcc_analysis(
            args=[analysis_id],
            retry=True,
            retry_policy={
                'max_retries': max_retries,
                'interval_start': 60,  # Start retrying after 60 seconds
                'interval_step': 60,   # Increase interval by 60 seconds each retry
                'interval_max': 300,   # Maximum interval of 5 minutes
            }
        )
        
        return result.get()
        
    except Exception as e:
        error_msg = f"Error in retry MCC analysis task: {str(e)}"
        logger.error(error_msg, error=e)
        
        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "analysis_id": analysis_id
        }


@celery_app.task(name="health_check_mcc")
def health_check_mcc():
    """
    Health check task for MCC analysis system
    
    Returns:
        dict: Health status
    """
    try:
        # Basic health check - just return success
        return {
            "status": "healthy",
            "service": "mcc_analysis",
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "mcc_analysis",
            "error": str(e),
            "timestamp": time.time()
        }

@celery_app.task(bind=True, name="process_url_classification")
def process_url_classification(self, website_id: int, website: str, scrape_request_ref_id: str, org_id: str = "default"):
    """
    Celery task to process URL classification
    
    Args:
        website_id (int): The ID of the website to analyze
        website (str): The URL of the website
        scrape_request_ref_id (str): Reference ID for the scrape request
        org_id (str): Organization ID
    
    Returns:
        dict: Result of the URL classification with status and details
    """
    
    logger = ConsoleLogger(f"{website_id}_{scrape_request_ref_id}")
    logger.info(f"Starting URL classification task for website: {website}")
    
    start_time = time.time()
    
    try:
        # Import required modules
        from sqlmodel import Session, select
        from app.models.db_models import WebsiteUrls
        from app.database import engine
        from app.services.url_classification import urlclassification_service
        import asyncio

        # Create URL classification service instance
        url_classification_service = urlclassification_service(
            website=website,
            scrape_request_ref_id=scrape_request_ref_id,
            org_id=org_id
        )
        
        # Process the URL classification using asyncio to run the async method
        result = asyncio.run(url_classification_service.process_classification())
        
        processing_time = time.time() - start_time
        logger.info(f"URL classification task completed in {processing_time:.2f} seconds")
        
        # Add task execution details to result
        result.update({
            "task_id": self.request.id,
            "website_id": website_id,
            "execution_time": processing_time
        })
        
        return result
        
    except Exception as e:
        error_msg = f"Error in URL classification task: {str(e)}"
        logger.error(error_msg, error=e)
        
        # Log the full traceback for debugging
        logger.error("Full traceback:", error=traceback.format_exc())
        
        processing_time = time.time() - start_time
        
        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "website_id": website_id,
            "website": website,
            "scrape_request_ref_id": scrape_request_ref_id,
            "execution_time": processing_time
        }

@celery_app.task(name="health_check_url_classification")
def health_check_url_classification():
    """
    Health check task for URL classification system
    
    Returns:
        dict: Health status
    """
    try:
        # Basic health check - just return success
        return {
            "status": "healthy",
            "service": "url_classification",
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "url_classification",
            "error": str(e),
            "timestamp": time.time()
        }

@celery_app.task(bind=True, name="retry_url_classification")
def retry_url_classification(self, website_id: int, website: str, scrape_request_ref_id: str, org_id: str = "default", max_retries: int = 3):
    """
    Celery task to retry URL classification with automatic retry logic
    
    Args:
        website_id (int): The ID of the website to analyze
        website (str): The URL of the website
        scrape_request_ref_id (str): Reference ID for the scrape request
        org_id (str): Organization ID
        max_retries (int): Maximum number of retries
        
    Returns:
        dict: Result of the URL classification with status and details
    """
    
    logger = ConsoleLogger(f"{website_id}_{scrape_request_ref_id}")
    
    try:
        # Use Celery's built-in retry mechanism
        result = process_url_classification(
            args=[website_id, website, scrape_request_ref_id, org_id],
            retry=True,
            retry_policy={
                'max_retries': max_retries,
                'interval_start': 60,  # Start retrying after 60 seconds
                'interval_step': 60,   # Increase interval by 60 seconds each retry
                'interval_max': 300,   # Maximum interval of 5 minutes
            }
        )
        
        return result.get()
        
    except Exception as e:
        error_msg = f"Error in retry URL classification task: {str(e)}"
        logger.error(error_msg, error=e)
        
        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "website_id": website_id,
            "website": website,
            "scrape_request_ref_id": scrape_request_ref_id
        }


@celery_app.task(bind=True, name="process_url_classification_v2")
def process_url_classification_v2(self, website: str, scrape_request_ref_id: str, org_id: str = "default"):
    """
    Celery task to process URL classification v2 (without website_id dependency)

    Args:
        website (str): The URL of the website
        scrape_request_ref_id (str): Reference ID for the scrape request
        org_id (str): Organization ID

    Returns:
        dict: Result of the URL classification with status and details
    """

    logger = ConsoleLogger(f"{scrape_request_ref_id}")
    logger.info(f"Starting URL classification v2 task for website: {website}")

    start_time = time.time()

    try:
        # Import required modules
        from sqlmodel import Session, select
        from app.models.db_models import WebsiteUrls
        from app.database import engine
        from app.services.url_classification import urlclassification_service
        import asyncio

        # Create URL classification service instance
        # Note: The service will use scrape_request_ref_id as the primary identifier
        url_classification_service = urlclassification_service(
            website=website,
            scrape_request_ref_id=scrape_request_ref_id,
            org_id=org_id
        )

        # Process the URL classification using asyncio to run the async method
        result = asyncio.run(url_classification_service.process_classification())

        processing_time = time.time() - start_time
        logger.info(f"URL classification v2 task completed in {processing_time:.2f} seconds")

        # Add task execution details to result
        result.update({
            "task_id": self.request.id,
            "website": website,
            "scrape_request_ref_id": scrape_request_ref_id,
            "execution_time": processing_time
        })

        return result

    except Exception as e:
        error_msg = f"Error in URL classification v2 task: {str(e)}"
        logger.error(error_msg, error=e)

        # Log the full traceback for debugging
        logger.error("Full traceback:", error=traceback.format_exc())

        processing_time = time.time() - start_time

        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "website": website,
            "scrape_request_ref_id": scrape_request_ref_id,
            "execution_time": processing_time
        }


@celery_app.task(
    bind=True,
    name="process_policy_analysis_enhanced",
    time_limit=2400,  # 40 minutes hard timeout
    soft_time_limit=2100,  # 35 minutes soft timeout
    max_retries=2,
    retry_backoff=True,
    retry_backoff_max=600,
    acks_late=True,
    track_started=True
)
def process_policy_analysis_enhanced(self, analysis_id):
    """
    Celery task to process enhanced policy analysis using new screenshot service with conditional popup handling

    Args:
        analysis_id (int): ID of the PolicyAnalysisNew record to process

    Returns:
        dict: Result of the analysis with status and details
    """
    import signal
    import asyncio
    from celery.exceptions import SoftTimeLimitExceeded
    from app.utils.process_cleanup import ProcessCleanupContext, celery_task_cleanup

    start_time = time.time()

    # Initialize logger first
    logger = ConsoleLogger(analysis_id)

    # Use process cleanup context for automatic cleanup
    with ProcessCleanupContext(cleanup_callback=celery_task_cleanup):
        try:
            logger.info(f"Starting enhanced policy analysis task for analysis_id: {analysis_id}")

            # Import required modules
            from sqlmodel import Session, select
            from app.models.db_models import PolicyAnalysisNew
            from app.database import engine

            # Extract values from database first
            scrape_request_ref_id = None
            org_id = None
            website = None

            with Session(engine) as session:
                # Get the PolicyAnalysisNew record
                analysis = session.get(PolicyAnalysisNew, analysis_id)

                if not analysis:
                    error_msg = f"PolicyAnalysisNew record with id {analysis_id} not found"
                    logger.error(error_msg)
                    return {
                        "status": "FAILED",
                        "error": error_msg,
                        "task_id": self.request.id,
                        "analysis_id": analysis_id
                    }

                # Extract values while object is still attached to session
                scrape_request_ref_id = analysis.scrape_request_ref_id
                org_id = analysis.org_id
                website = analysis.website

                logger.info(f"Found PolicyAnalysisNew record: {website}, ref_id: {scrape_request_ref_id}")

                # Update analysis status
                analysis.processing_status = "PROCESSING"
                analysis.started_at = datetime.now().isoformat() + "Z"
                session.commit()
                logger.info(f"Updated PolicyAnalysisNew status to PROCESSING")

            # Initialize the Enhanced Policy Analysis service using extracted values
            logger.info(f"Initializing Enhanced Policy Analysis service")
            policy_service = PolicyAnalysisEnhancedService(
                scrape_request_ref_id=scrape_request_ref_id,
                org_id=org_id
            )

            # Process the Enhanced Policy Analysis (includes backup flow logic and conditional popup handling)
            logger.info(f"Running Enhanced Policy Analysis with conditional popup handling")

            # Run with timeout to prevent hanging
            async def run_with_timeout():
                return await asyncio.wait_for(
                    policy_service.process_policy_analysis(),
                    timeout=2000  # 33 minutes timeout (less than soft limit)
                )

            try:
                result = asyncio.run(run_with_timeout())
            except asyncio.TimeoutError:
                logger.error("Policy analysis timed out after 33 minutes")
                raise Exception("Policy analysis process timed out")

            processing_time = time.time() - start_time
            logger.info(f"Enhanced Policy Analysis task completed in {processing_time:.2f} seconds with status: {result.get('status')}")

            # 🚀 NEW: Send webhook for both COMPLETED and FAILED status
            webhook_sent = False
            try:
                logger.info("")
                logger.info("📤 SENDING POLICY RESULTS WEBHOOK")
                logger.info("=" * 50)

                # Import webhook function
                from app.utils.webhook_utils import send_policy_results_webhook

                if result.get("status") == "COMPLETED":
                    # Send successful webhook with actual data
                    logger.info("✅ Sending successful policy analysis webhook")
                    webhook_sent = result.get("webhook_sent", False)
                else:
                    # Send default webhook for failed analysis
                    logger.info("⚠️ Sending default webhook for failed policy analysis")

                    # Create default webhook data for failed analysis
                    default_webhook_data = {
                        "website": website,
                        "scrapeRequestUuid": scrape_request_ref_id,
                        "createdDate": datetime.now().isoformat() + "Z",
                        "status": "COMPLETED",  # Send as COMPLETED with insufficient data
                        "policies": [
                            {"type": "RAC", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "PP", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "SD", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "CU", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "IG", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "YT", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "FB", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "TW", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "LI", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                            {"type": "PT", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"}
                        ],
                        "org_id": int(org_id) if str(org_id).isdigit() else 1
                    }

                    logger.info(f"📋 Prepared default webhook data with {len(default_webhook_data['policies'])} policies")
                    logger.info(f"🌐 Target website: {website}")
                    logger.info(f"🆔 Request ID: {scrape_request_ref_id}")
                    logger.info(f"⚠️ Reason: {result.get('error', 'Analysis failed')}")

                    # Send the default webhook
                    logger.info("🚀 Sending default webhook request...")
                    webhook_sent = send_policy_results_webhook(
                        scrape_request_ref_id=scrape_request_ref_id,
                        website=website,
                        policy_result=default_webhook_data,
                        analysis_id=analysis_id,
                        logger=logger
                    )

                if webhook_sent:
                    logger.info("✅ SUCCESS: Policy results webhook sent successfully")
                    logger.info("=" * 50)
                else:
                    logger.error("❌ FAILED: Policy results webhook failed")
                    logger.error("=" * 50)

            except Exception as webhook_error:
                logger.error(f"❌ Error sending Policy results webhook: {str(webhook_error)}")
                webhook_sent = False

            return {
                "status": result.get("status", "COMPLETED"),
                "task_id": self.request.id,
                "website": website,
                "scrape_request_ref_id": scrape_request_ref_id,
                "analysis_id": analysis_id,
                "execution_time": processing_time,
                "analysis_flow": result.get("analysis_flow"),
                "reachability_percentage": result.get("reachability_percentage"),
                "categories_processed": result.get("categories_processed"),
                "webhook_sent": webhook_sent
            }

        except SoftTimeLimitExceeded:
            logger.warning("Task soft time limit exceeded - attempting graceful shutdown")
            celery_task_cleanup()
            processing_time = time.time() - start_time

            # Update database status
            try:
                from sqlmodel import Session
                from app.models.db_models import PolicyAnalysisNew
                from app.database import engine
                with Session(engine) as session:
                    analysis = session.get(PolicyAnalysisNew, analysis_id)
                    if analysis:
                        analysis.processing_status = "TIMEOUT"
                        analysis.error_message = "Task exceeded time limit"
                        session.commit()
            except Exception as db_error:
                logger.error(f"Failed to update database on timeout: {db_error}")

            # 🚀 NEW: Send default webhook even when task times out
            webhook_sent = False
            try:
                logger.info("⚠️ Sending default webhook for timed out policy analysis")

                # Import webhook function
                from app.utils.webhook_utils import send_policy_results_webhook

                # Create default webhook data for timed out analysis
                default_webhook_data = {
                    "website": website if 'website' in locals() else "insufficient data",
                    "scrapeRequestUuid": scrape_request_ref_id if 'scrape_request_ref_id' in locals() else "unknown",
                    "createdDate": datetime.now().isoformat() + "Z",
                    "status": "COMPLETED",  # Send as COMPLETED with insufficient data
                    "policies": [
                        {"type": "RAC", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "PP", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "SD", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "CU", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "IG", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "YT", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "FB", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "TW", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "LI", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "PT", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"}
                    ],
                    "org_id": int(org_id) if 'org_id' in locals() and str(org_id).isdigit() else 1
                }

                # Send the default webhook
                webhook_sent = send_policy_results_webhook(
                    scrape_request_ref_id=default_webhook_data['scrapeRequestUuid'],
                    website=default_webhook_data['website'],
                    policy_result=default_webhook_data,
                    analysis_id=analysis_id,
                    logger=logger
                )

                if webhook_sent:
                    logger.info("✅ SUCCESS: Default policy webhook sent for timed out analysis")
                else:
                    logger.error("❌ FAILED: Default policy webhook failed")

            except Exception as webhook_error:
                logger.error(f"❌ Error sending default Policy webhook for timeout: {str(webhook_error)}")
                webhook_sent = False

            return {
                "status": "TIMEOUT",
                "error": "Task exceeded soft time limit",
                "task_id": self.request.id,
                "analysis_id": analysis_id,
                "execution_time": processing_time,
                "webhook_sent": webhook_sent
            }

        except Exception as e:
            error_msg = f"Error in enhanced policy analysis task: {str(e)}"
            logger.error(error_msg, error=e)

            # Log the full traceback for debugging
            logger.error("Full traceback:", error=traceback.format_exc())

            processing_time = time.time() - start_time

            # 🚀 NEW: Send default webhook even when task fails with exception
            webhook_sent = False
            try:
                logger.info("⚠️ Sending default webhook for failed policy analysis (exception case)")

                # Import webhook function
                from app.utils.webhook_utils import send_policy_results_webhook

                # Create default webhook data for failed analysis
                default_webhook_data = {
                    "website": website if 'website' in locals() else "insufficient data",
                    "scrapeRequestUuid": scrape_request_ref_id if 'scrape_request_ref_id' in locals() else "unknown",
                    "createdDate": datetime.now().isoformat() + "Z",
                    "status": "COMPLETED",  # Send as COMPLETED with insufficient data
                    "policies": [
                        {"type": "RAC", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "PP", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "SD", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "CU", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "IG", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "YT", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "FB", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "TW", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "LI", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                        {"type": "PT", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"}
                    ],
                    "org_id": int(org_id) if 'org_id' in locals() and str(org_id).isdigit() else 1
                }

                logger.info(f"📋 Prepared default webhook data for exception case")
                logger.info(f"🌐 Target website: {default_webhook_data['website']}")
                logger.info(f"🆔 Request ID: {default_webhook_data['scrapeRequestUuid']}")
                logger.info(f"⚠️ Reason: {error_msg}")

                # Send the default webhook
                webhook_sent = send_policy_results_webhook(
                    scrape_request_ref_id=default_webhook_data['scrapeRequestUuid'],
                    website=default_webhook_data['website'],
                    policy_result=default_webhook_data,
                    analysis_id=analysis_id,
                    logger=logger
                )

                if webhook_sent:
                    logger.info("✅ SUCCESS: Default policy webhook sent for failed analysis")
                else:
                    logger.error("❌ FAILED: Default policy webhook failed")

            except Exception as webhook_error:
                logger.error(f"❌ Error sending default Policy webhook: {str(webhook_error)}")
                webhook_sent = False

            return {
                "status": "FAILED",
                "error": error_msg,
                "task_id": self.request.id,
                "analysis_id": analysis_id,
                "execution_time": processing_time,
                "webhook_sent": webhook_sent
            }


@celery_app.task(
    bind=True,
    name="process_policy_analysis",
    time_limit=2400,  # 40 minutes hard timeout
    soft_time_limit=2100,  # 35 minutes soft timeout
    max_retries=2,
    retry_backoff=True,
    retry_backoff_max=600,
    acks_late=True,
    track_started=True
)
def process_policy_analysis(self, analysis_id):
    """
    Celery task to process policy analysis with backup flow support

    Args:
        analysis_id (int): ID of the PolicyAnalysisNew record to process

    Returns:
        dict: Result of the analysis with status and details
    """

    logger = ConsoleLogger(analysis_id)
    logger.info(f"Starting Policy Analysis task for analysis_id: {analysis_id}")

    start_time = time.time()

    try:
        # Get the PolicyAnalysisNew record from the database
        from sqlmodel import Session, select
        from app.models.db_models import PolicyAnalysisNew
        from app.database import engine

        logger.info(f"Importing required modules for Policy Analysis")

        # Import the service here to avoid circular import
        from app.services.policy_analysis_new_service import PolicyAnalysisNewService

        logger.info(f"Opening database session to get PolicyAnalysisNew record")
        with Session(engine) as session:
            # Get the PolicyAnalysisNew record
            analysis = session.get(PolicyAnalysisNew, analysis_id)
            if not analysis:
                error_msg = f"PolicyAnalysisNew with ID {analysis_id} not found"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Extract values while object is still attached to session
            scrape_request_ref_id = analysis.scrape_request_ref_id
            org_id = analysis.org_id
            website = analysis.website

            logger.info(f"Found PolicyAnalysisNew record: {website}, ref_id: {scrape_request_ref_id}")

            # Update analysis status
            analysis.processing_status = "PROCESSING"
            analysis.started_at = datetime.now().isoformat() + "Z"
            session.commit()
            logger.info(f"Updated PolicyAnalysisNew status to PROCESSING")

        # Initialize the Policy Analysis service using extracted values
        logger.info(f"Initializing Policy Analysis service")
        policy_service = PolicyAnalysisNewService(
            scrape_request_ref_id=scrape_request_ref_id,
            org_id=org_id
        )

        # Process the Policy Analysis (includes backup flow logic)
        logger.info(f"Running Policy Analysis with backup flow support")
        result = asyncio.run(policy_service.process_policy_analysis())

        processing_time = time.time() - start_time
        logger.info(f"Policy Analysis task completed in {processing_time:.2f} seconds with status: {result.get('status')}")

        # Update final status in database
        with Session(engine) as session:
            analysis = session.get(PolicyAnalysisNew, analysis_id)
            if analysis:
                if result.get('status') == 'COMPLETED':
                    analysis.processing_status = "COMPLETED"
                    analysis.result_status = "COMPLETED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                else:
                    analysis.processing_status = "FAILED"
                    analysis.result_status = "FAILED"
                    analysis.failed_at = datetime.now().isoformat() + "Z"
                    analysis.error_message = result.get('error', 'Unknown error')

                analysis.last_updated = datetime.now().isoformat() + "Z"
                session.commit()
                logger.info(f"Updated PolicyAnalysisNew final status to {analysis.processing_status}")

        # 🚀 NEW: Send Policy results webhook after successful analysis
        if result.get("status") == "COMPLETED":
            try:
                logger.info("")
                logger.info("📤 SENDING POLICY RESULTS WEBHOOK")
                logger.info("=" * 50)

                # Import webhook function
                from app.utils.webhook_utils import send_policy_results_webhook

                # Get the updated analysis record for webhook data
                with Session(engine) as session:
                    analysis = session.get(PolicyAnalysisNew, analysis_id)
                    if analysis:
                        # Prepare webhook data from analysis results
                        # Use ISO string format (already in correct format from database)
                        created_date_iso = analysis.created_at if analysis.created_at else datetime.now().isoformat() + "Z"
                        
                        policy_webhook_data = {
                            "website": analysis.website,
                            "scrapeRequestUuid": analysis.scrape_request_ref_id,
                            "createdDate": created_date_iso,
                            "status": "COMPLETED",
                            "policies": [],
                            "org_id": analysis.org_id
                        }

                        # Extract policy information from the analysis record
                        policy_categories = [
                            ("returns_cancellation_exchange", "RAC"),
                            ("privacy_policy", "PP"),
                            ("shipping_delivery", "SD"),
                            ("contact_us", "CU"),
                            ("about_us", "AU"),
                            ("instagram", "IG"),
                            ("youtube", "YT"),
                            ("facebook", "FB"),
                            ("twitter", "TW"),
                            ("linkedin", "LI"),
                            ("pinterest", "PT")
                        ]

                        for db_field, policy_type in policy_categories:
                            url_field = f"{db_field}_url"
                            text_field = f"{db_field}_text"
                            screenshot_field = f"{db_field}_screenshot"

                            # Always get values with defaults
                            url = getattr(analysis, url_field, "insufficient data")
                            text = getattr(analysis, text_field, "insufficient data")
                            screenshot = getattr(analysis, screenshot_field, "insufficient data")

                            # Truncate text to maximum 100 words for API compatibility
                            if text and text != "insufficient data":
                                text = _truncate_text_to_words(text, 100)

                            # Always include policy entry, even with insufficient data
                            policy_webhook_data["policies"].append({
                                "type": policy_type,
                                "url": url if url else "insufficient data",
                                "imglink": screenshot if screenshot and screenshot != "insufficient data" else "insufficient data",
                                "text": text if text and text != "insufficient data" else "insufficient data"
                            })

                        logger.info(f"📋 Prepared webhook data with {len(policy_webhook_data['policies'])} policies")
                        logger.info(f"🌐 Target website: {analysis.website}")
                        logger.info(f"🆔 Request ID: {scrape_request_ref_id}")

                        # Send the webhook
                        logger.info("🚀 Sending webhook request...")
                        webhook_success = send_policy_results_webhook(
                            scrape_request_ref_id=scrape_request_ref_id,
                            website=analysis.website,
                            policy_result=policy_webhook_data,
                            analysis_id=analysis_id,
                            logger=logger
                        )

                        if webhook_success:
                            logger.info("✅ SUCCESS: Policy results webhook sent successfully")
                            logger.info("=" * 50)
                        else:
                            logger.error("❌ FAILED: Policy results webhook failed")
                            logger.error("=" * 50)
                    else:
                        logger.error("❌ Could not retrieve analysis record for webhook")

                        # Send default webhook even if analysis record is missing
                        logger.info("🔄 Sending default webhook data")
                        default_webhook_data = {
                            "website": "insufficient data",
                            "scrapeRequestUuid": scrape_request_ref_id,
                            "createdDate": datetime.now().isoformat() + "Z",
                            "status": "COMPLETED",
                            "policies": [
                                {"type": "RAC", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "PP", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "SD", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "CU", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "IG", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "YT", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "FB", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "TW", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "LI", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                                {"type": "PT", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"}
                            ],
                            "org_id": "default"
                        }

                        webhook_success = send_policy_results_webhook(
                            scrape_request_ref_id=scrape_request_ref_id,
                            website="insufficient data",
                            policy_result=default_webhook_data,
                            analysis_id=analysis_id,
                            logger=logger
                        )

            except Exception as webhook_error:
                logger.error(f"❌ Error sending Policy results webhook: {str(webhook_error)}")
                # Don't fail the task if webhook fails - the analysis was completed
        else:
            logger.info("⚠️ Skipping webhook send due to non-COMPLETED status", {"status": result.get("status")})

        return result

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Policy Analysis task failed after {processing_time:.2f} seconds: {str(e)}")

        # Check if this is a database-related error that should trigger a retry
        error_str = str(e).lower()
        is_database_error = any(keyword in error_str for keyword in [
            'database', 'connection', 'timeout', 'deadlock', 'lock', 'sql'
        ])

        if is_database_error and self.request.retries < self.max_retries:
            logger.warning(
                f"Database error detected, retrying task (attempt {self.request.retries + 1}/{self.max_retries + 1})",
                {
                    "error": str(e),
                    "error_type": "database_error",
                    "retry_attempt": self.request.retries + 1,
                    "max_retries": self.max_retries + 1
                }
            )
            # Retry with exponential backoff
            raise self.retry(exc=e, countdown=120 * (2 ** self.request.retries))

        # Update error status in database with retry logic
        max_db_retries = 3
        for db_attempt in range(max_db_retries):
            try:
                with Session(engine) as session:
                    analysis = session.get(PolicyAnalysisNew, analysis_id)
                    if analysis:
                        analysis.processing_status = "FAILED"
                        analysis.result_status = "FAILED"
                        analysis.failed_at = datetime.now().isoformat() + "Z"
                        analysis.error_message = str(e)
                        analysis.last_updated = datetime.now().isoformat() + "Z"
                        session.commit()
                        logger.info(f"Updated PolicyAnalysisNew status to FAILED due to error (attempt {db_attempt + 1})")
                        break
            except Exception as db_error:
                logger.error(
                    f"Failed to update error status in database (attempt {db_attempt + 1}/{max_db_retries}): {str(db_error)}"
                )
                if db_attempt < max_db_retries - 1:
                    time.sleep(2 ** db_attempt)  # Exponential backoff
                else:
                    logger.error("All database update attempts failed")

        return {"status": "failed", "error": str(e), "analysis_id": analysis_id}


@celery_app.task(bind=True, name="process_risky_classification")
def process_risky_classification(self, analysis_id):
    """
    Celery task to process risky classification with dual flow architecture

    Args:
        analysis_id (int): ID of the RiskyAnalysis record to process

    Returns:
        dict: Result of the analysis with status and details
    """
    
    logger = ConsoleLogger(analysis_id)
    logger.info(f"Starting risky classification task for analysis_id: {analysis_id}")

    start_time = time.time()

    try:
        # Get analysis record and extract values
        with Session(engine) as session:
            analysis = session.get(RiskyAnalysis, analysis_id)

            if not analysis:
                error_msg = f"RiskyAnalysis record not found for ID: {analysis_id}"
                logger.error(error_msg)
                return {
                    "status": "FAILED",
                    "error": error_msg,
                    "task_id": self.request.id,
                    "analysis_id": analysis_id
                }

            # Extract values while object is still attached to session
            scrape_request_ref_id = analysis.scrape_request_ref_id
            org_id = analysis.org_id
            website = analysis.website

            logger.info(f"Found RiskyAnalysis record: {website}, ref_id: {scrape_request_ref_id}")

            # Update analysis status
            analysis.processing_status = "PROCESSING"
            analysis.started_at = datetime.now().isoformat() + "Z"
            session.commit()
            logger.info(f"Updated RiskyAnalysis status to PROCESSING")

        # Initialize the Risky Classification service using extracted values
        logger.info(f"Initializing Risky Classification service")
        risky_service = RiskyClassificationService(
            scrape_request_ref_id=scrape_request_ref_id,
            org_id=org_id
        )

        # Process the Risky Classification (includes dual flow logic)
        logger.info(f"Running Risky Classification with dual flow architecture")
        result = asyncio.run(risky_service.process_risky_classification())

        processing_time = time.time() - start_time
        logger.info(f"Risky Classification task completed in {processing_time:.2f} seconds with status: {result.get('status')}")

        # DUPLICATION MONITORING: Track analysis_id usage
        actual_analysis_id = result.get("analysis_id", analysis_id)
        logger.info(f"DUPLICATION MONITORING: Using analysis_id {actual_analysis_id} for webhook (original router-created: {analysis_id})")

        if actual_analysis_id != analysis_id:
            logger.warning(f"DUPLICATION DETECTED: Service created new record {actual_analysis_id}, router created {analysis_id}")
        else:
            logger.info(f"DUPLICATION FIX SUCCESS: Service updated existing record {analysis_id}")

        # DUPLICATION VERIFICATION: Check total record count for this scrape_request_ref_id
        try:
            with Session(engine) as verification_session:
                all_records = verification_session.exec(
                    select(RiskyAnalysis).where(
                        RiskyAnalysis.scrape_request_ref_id == scrape_request_ref_id
                    )
                ).all()

                logger.info(f"DUPLICATION VERIFICATION: Total records for ref_id {scrape_request_ref_id}: {len(all_records)}")

                if len(all_records) > 1:
                    logger.error(f"DUPLICATION STILL EXISTS: Found {len(all_records)} records for ref_id {scrape_request_ref_id}")
                    for record in all_records:
                        logger.error(f"  - Record ID {record.id}: status={record.processing_status}, flow={record.analysis_flow_used}, created={record.created_at}")
                else:
                    logger.info(f"DUPLICATION FIX VERIFIED: Only 1 record exists for ref_id {scrape_request_ref_id}")

        except Exception as verification_error:
            logger.error(f"Error during duplication verification: {verification_error}")

        # Update final status in database with retry logic using the correct analysis_id
        max_db_retries = 3
        for db_attempt in range(max_db_retries):
            try:
                with Session(engine) as session:
                    analysis = session.get(RiskyAnalysis, actual_analysis_id)
                    if analysis:
                        if result.get("status") == "COMPLETED":
                            analysis.processing_status = "COMPLETED"
                            analysis.completed_at = datetime.now().isoformat() + "Z"
                            logger.info(f"Updated RiskyAnalysis status to COMPLETED (attempt {db_attempt + 1})")
                        else:
                            # Enhanced failure handling with specific codes
                            failure_code = result.get("failure_code", "FAILED")
                            analysis.processing_status = failure_code
                            analysis.error_message = result.get("error", "Unknown error")

                            # Update analysis_flow_used for failed cases
                            if failure_code in ["FAILED_DUAL_FLOW", "FAILED_NORMAL_FLOW", "FAILED_BACKUP_FLOW"]:
                                analysis.analysis_flow_used = "failed_both_flows"
                                analysis.overall_result = json.dumps({
                                    "status": "failed",
                                    "reason": "both_normal_and_backup_flows_failed",
                                    "flow_attempted": "both",
                                    "error_message": result.get("error", "Unknown error")
                                })

                            logger.info(f"Updated RiskyAnalysis status to {failure_code} (attempt {db_attempt + 1})")

                        session.commit()
                        break
            except Exception as db_error:
                logger.error(
                    f"Failed to update final status in database (attempt {db_attempt + 1}/{max_db_retries}): {str(db_error)}"
                )
                if db_attempt < max_db_retries - 1:
                    time.sleep(2 ** db_attempt)  # Exponential backoff
                else:
                    logger.error("All database update attempts failed")

        # Send webhook if analysis completed successfully
        if result.get("status") == "COMPLETED":
            try:
                logger.info("🚀 Sending risky classification webhook...")

                # Get the updated analysis record for webhook data using the correct analysis_id
                with Session(engine) as session:
                    analysis = session.get(RiskyAnalysis, actual_analysis_id)
                    if analysis:
                        # Prepare webhook data from analysis results
                        risky_webhook_data = {
                            "website": analysis.website,
                            "scrape_request_ref_id": analysis.scrape_request_ref_id,
                            "is_risky": analysis.is_risky,
                            "risk_categories": analysis.risk_categories,
                            "keywords_found": analysis.keywords_found,
                            "reason_for_risky": analysis.reason_for_risky,
                            "analysis_details": analysis.analysis_details,
                            "analysis_flow_used": analysis.analysis_flow_used,
                            "url_results": []  # Will be populated from analysis_details
                        }

                        # Parse analysis details to get URL results
                        if analysis.analysis_details:
                            try:
                                analysis_data = json.loads(analysis.analysis_details)
                                risky_webhook_data["url_results"] = analysis_data.get("url_results", [])
                            except json.JSONDecodeError:
                                logger.warning("Failed to parse analysis_details for webhook")

                        logger.info(f"📋 Prepared risky classification webhook data")
                        logger.info(f"🌐 Target website: {analysis.website}")
                        logger.info(f"🆔 Request ID: {scrape_request_ref_id}")
                        logger.info(f"⚠️ Risky status: {analysis.is_risky}")

                        # Debug logging for reason_for_risky data flow
                        logger.info(
                            "🔍 REASON_FOR_RISKY DEBUG - Database to Webhook Data Flow",
                            {
                                "db_reason_for_risky": analysis.reason_for_risky,
                                "db_keywords_found": analysis.keywords_found,
                                "db_risk_categories": analysis.risk_categories,
                                "webhook_data_keys": list(risky_webhook_data.keys()),
                                "webhook_reason_for_risky": risky_webhook_data.get("reason_for_risky"),
                                "webhook_keywords_found": risky_webhook_data.get("keywords_found")
                            }
                        )

                        # Send the webhook using the correct analysis_id
                        webhook_success = send_risky_classification_webhook(
                            scrape_request_ref_id=scrape_request_ref_id,
                            website=analysis.website,
                            risky_result=risky_webhook_data,
                            analysis_id=actual_analysis_id,
                            logger=logger
                        )

                        if webhook_success:
                            logger.info("✅ SUCCESS: Risky classification webhook sent successfully")
                        else:
                            logger.error("❌ FAILED: Risky classification webhook failed")
                    else:
                        logger.error("❌ Could not retrieve analysis record for webhook")

            except Exception as webhook_error:
                logger.error(f"❌ Error sending risky classification webhook: {str(webhook_error)}")
                # Don't fail the entire task if webhook fails
                logger.error("Webhook error traceback:", error=traceback.format_exc())

        return {
            "status": result.get("status", "FAILED"),
            "task_id": self.request.id,
            "analysis_id": actual_analysis_id,  # Use the correct analysis_id
            "execution_time": processing_time,
            "flow_used": result.get("flow_used"),
            "is_risky": result.get("is_risky", False)
        }

    except Exception as e:
        error_msg = f"Error in risky classification task: {str(e)}"
        logger.error(error_msg, error=e)

        # Log the full traceback for debugging
        logger.error("Full traceback:", error=traceback.format_exc())

        processing_time = time.time() - start_time

        # Update error status in database with retry logic
        max_db_retries = 3
        for db_attempt in range(max_db_retries):
            try:
                with Session(engine) as session:
                    analysis = session.get(RiskyAnalysis, analysis_id)
                    if analysis:
                        analysis.processing_status = "FAILED"
                        analysis.error_message = str(e)
                        analysis.completed_at = datetime.now().isoformat() + "Z"
                        session.commit()
                        logger.info(f"Updated RiskyAnalysis status to FAILED due to error (attempt {db_attempt + 1})")
                        break
            except Exception as db_error:
                logger.error(
                    f"Failed to update error status in database (attempt {db_attempt + 1}/{max_db_retries}): {str(db_error)}"
                )
                if db_attempt < max_db_retries - 1:
                    time.sleep(2 ** db_attempt)  # Exponential backoff
                else:
                    logger.error("All database update attempts failed")

        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "analysis_id": analysis_id,
            "execution_time": processing_time
        }
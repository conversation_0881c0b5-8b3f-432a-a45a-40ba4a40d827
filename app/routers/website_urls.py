from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlmodel import Session, select
from app.models.db_models import WebsiteUrls, Website, ScrapeRequestTracker
from app.database import get_session
from typing import List, Dict, Any

router = APIRouter()

@router.post("/", response_model=WebsiteUrls)
def create_website_url(website_url: WebsiteUrls, session: Session = Depends(get_session)):
    session.add(website_url)
    session.commit()
    session.refresh(website_url)
    return website_url

@router.get("/{id}", response_model=WebsiteUrls)
def read_website_url(id: int, session: Session = Depends(get_session)):
    url = session.get(WebsiteUrls, id)
    if not url:
        raise HTTPException(status_code=404, detail="WebsiteUrl not found")
    return url

@router.get("/", response_model=list[WebsiteUrls])
def list_website_urls(session: Session = Depends(get_session)):
    urls = session.exec(select(WebsiteUrls)).all()
    return urls

@router.delete("/{id}")
def delete_website_url(id: int, session: Session = Depends(get_session)):
    url = session.get(WebsiteUrls, id)
    if not url:
        raise HTTPException(status_code=404, detail="WebsiteUrl not found")
    session.delete(url)
    session.commit()
    return {"message": "WebsiteUrl deleted successfully"}

@router.get("/by-scrape-ref/{scrape_request_ref_id}", response_model=List[Dict[str, Any]])
def get_website_urls_by_scrape_ref(scrape_request_ref_id: str, session: Session = Depends(get_session)):
    """
    Get all website URLs associated with a specific scrape request reference ID
    """
    # Get the tracker to find the website ID
    tracker = session.exec(
        select(ScrapeRequestTracker).where(
            ScrapeRequestTracker.scrape_request_ref_id == scrape_request_ref_id
        )
    ).first()
    
    if not tracker:
        raise HTTPException(
            status_code=404,
            detail=f"No tracker found for scrape_request_ref_id {scrape_request_ref_id}"
        )
    
    # If the analysis_id is set (website_id), use it to get URLs
    if tracker.analysis_id:
        urls = session.exec(
            select(WebsiteUrls).where(
                WebsiteUrls.website_ref == tracker.analysis_id
            )
        ).all()
        
        return [
            {
                "url": url.url,
                "soft_class": url.soft_class,
                "priority_url": url.priority_url,
                "depth": url.depth
            }
            for url in urls
        ]
    else:
        return []
from datetime import datetime
import time
from typing import Optional

from sqlalchemy import Column, types
from sqlmodel import Field, SQLModel
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine

from app.config import settings

#dao

# Create engine and session
engine = create_engine(settings.DATABASE_URL)
Session = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_current_time():
    return datetime.now().isoformat() + "Z"  # Return ISO format string with Z suffix


# Define a custom MEDIUMTEXT type
class MediumText(types.TypeDecorator):
    impl = types.TEXT
    cache_ok = True  # Set cache_ok to True for better performance

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def load_dialect_impl(self, dialect):
        if dialect.name == "mysql":
            return dialect.type_descriptor(types.TEXT(length=16777215))
        else:
            return dialect.type_descriptor(types.TEXT())


class MccAnalysis(SQLModel, table=True):
    """
    MCC Analysis model - stores MCC analysis results
    """
    __tablename__ = 'mcc_analysis_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    website: str
    scrape_request_ref_id: str
    result_status: Optional[str] = Field(default=None)
    mcc_code: Optional[str] = Field(default=None)  # Single MCC code
    business_category: Optional[str] = Field(default=None)
    business_description: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    reasoning: Optional[str] = Field(default=None, sa_column=Column(MediumText))  # Reasoning for MCC classification
    created_at: Optional[str] = Field(default_factory=get_current_time)
    started_at: Optional[str] = Field(default=None)
    completed_at: Optional[str] = Field(default=None)
    failed_at: Optional[str] = Field(default=None)
    last_updated: Optional[str] = Field(default=None)
    error_message: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    details: Optional[str] = Field(default=None, sa_column=Column(MediumText))  
    org_id: str = Field(default="default")
    processing_status: str = Field(default="PENDING")  # PENDING, PROCESSING, COMPLETED, FAILED


class WebsiteUrls(SQLModel, table=True):
    __tablename__ = 'website_urls_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    scrape_request_ref_id: str = Field(index=True)  # Changed from website_ref to scrape_request_ref_id
    website: str
    url: str = Field(default="", sa_column=Column(MediumText))
    depth: int = 1
    soft_class: str = ""
    hard_class: str = ""  # New field for hard classification results
    priority_url: bool = False
    extracted_text: str = Field(default="", sa_column=Column(MediumText))
    img_url: str = Field(default="", sa_column=Column(MediumText))
    policy: str = Field(default="", sa_column=Column(MediumText))
    registered_name: str = Field(default="", sa_column=Column(MediumText))
    org_id: str = Field(default="default", nullable=True)

#Todo: combine tables GeminiApiLog and GeneralLogs
class GeneralLogs(SQLModel, table=True):
    __tablename__ = 'general_logs_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    analysis_id: int
    timestamp: str
    type: str
    messages: str = Field(sa_column=Column(MediumText))
    response: str = Field(sa_column=Column(MediumText))
    org_id: str = Field(default="default", nullable=True)

class GeminiApiLog(SQLModel, table=True):
    __tablename__ = 'gemini_api_log_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    request_id: str
    org_id: str = Field(default="default", nullable=True)
    model_name: str
    task_type: str  # mcc_analysis, policy_analysis, etc.
    input_text: str = Field(sa_column=Column(MediumText))
    output_text: str = Field(sa_column=Column(MediumText))
    prompt_token_count: int
    completion_token_count: int
    total_token_count: int
    cached_token_count: Optional[int] = Field(default=0, nullable=True)
    usage_metadata: str = Field(default="", sa_column=Column(MediumText))
    http_status: int = Field(default=200, nullable=True)
    error_message: Optional[str] = Field(default=None, nullable=True)
    request_time: str = Field(default_factory=get_current_time)
    response_time: Optional[str] = Field(default=None, nullable=True)
    processing_time_ms: Optional[int] = Field(default=None, nullable=True)
    website_url: Optional[str] = Field(default=None, nullable=True)
    cost_estimate: Optional[float] = Field(default=None, nullable=True)
    is_cached: bool = Field(default=False)

class RiskyAnalysis(SQLModel, table=True):
    """
    Risky Analysis model - stores risky classification results following MCC service patterns
    """
    __tablename__ = 'risky_analysis_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    website: str
    scrape_request_ref_id: str = Field(index=True)  # Primary tracking mechanism

    # Analysis flow tracking
    analysis_flow_used: Optional[str] = Field(default="not_determined", nullable=True)  # "normal", "backup", "failed_both_flows", "not_determined"
    reachability_percentage: Optional[float] = Field(default=None, nullable=True)
    total_urls_processed: Optional[int] = Field(default=0, nullable=True)

    # Risk assessment results - Enhanced defaults for failed states
    overall_result: str = Field(default='{"status": "not_processed", "reason": "analysis_not_started", "flow_attempted": "none"}', sa_column=Column(MediumText))
    is_risky: bool = False
    risk_categories: Optional[str] = Field(default="[]", sa_column=Column(MediumText))  # JSON string of categories
    keywords_found: Optional[str] = Field(default="[]", sa_column=Column(MediumText))  # JSON string of keywords
    reason_for_risky: Optional[str] = Field(default="[]", sa_column=Column(MediumText))  # JSON string of reasons
    analysis_details: Optional[str] = Field(default='{"status": "not_processed", "details": "analysis_not_started"}', sa_column=Column(MediumText))

    # Status and timing - Enhanced with specific failure codes
    processing_status: str = "PENDING"  # PENDING, PROCESSING, COMPLETED, FAILED, FAILED_DUAL_FLOW, FAILED_URL_UNREACHABLE, FAILED_GEMINI_ERROR, FAILED_TIMEOUT, FAILED_CLASSIFICATION_ERROR
    created_at: str = Field(default_factory=get_current_time)
    started_at: Optional[str] = Field(default=None, nullable=True)
    completed_at: Optional[str] = Field(default=None, nullable=True)

    # Error handling and organization
    error_message: Optional[str] = Field(default=None, nullable=True)
    org_id: str = Field(default="default", nullable=True)

class RiskyUrlAnalysis(SQLModel, table=True):
    """
    Model for storing individual URL risk analysis results
    """
    __tablename__ = 'risky_url_analysis_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    risky_analysis_id: int = Field(index=True)  # References RiskyAnalysis.id
    scrape_request_ref_id: str = Field(index=True)

    # URL information
    url: str = Field(sa_column=Column(MediumText))
    is_reachable: Optional[bool] = Field(default=None, nullable=True)  # For reachability tracking
    url_depth: int = Field(default=1)

    # Risk assessment
    is_risky: bool = False
    risk_categories: str = Field(default="[]")  # JSON string of categories
    analysis_details: str = Field(default="{}", sa_column=Column(MediumText))

    # Text extraction (for backup flow)
    extracted_text: Optional[str] = Field(default=None, sa_column=Column(MediumText, nullable=True))
    text_extraction_method: Optional[str] = Field(default=None, nullable=True)  # "requests" or "browser"

    # Metadata
    created_at: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)

class RiskyKeywordResult(SQLModel, table=True):
    __tablename__ = 'risky_keyword_result_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    url: str = Field(sa_column=Column(MediumText))
    source_website: str
    analysis_result: str = Field(sa_column=Column(MediumText))
    risky_categories: str = Field(default="", sa_column=Column(MediumText))
    created_at: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)

# New model to track all scrape request references across different analysis types
class ScrapeRequestTracker(SQLModel, table=True):
    __tablename__ = 'scrape_request_tracker_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    scrape_request_ref_id: str = Field(index=True)  # Indexed for faster lookups
    analysis_type: str  # Type of analysis: "mcc", "risky", "policy", etc.
    website: str
    analysis_id: Optional[int] = Field(default=None)  # ID of the associated analysis record
    created_at: str = Field(default_factory=get_current_time)
    completed_at: Optional[str] = Field(default=None, nullable=True)
    status: str = "PENDING"  # PENDING, PROCESSING, COMPLETED, FAILED
    error_message: Optional[str] = Field(default=None, nullable=True)
    org_id: str = Field(default="default", nullable=True)

# Add the missing ScrapedUrl model
class ScrapedUrl(SQLModel, table=True):
    __tablename__ = 'scraped_urls_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    url: str = Field(sa_column=Column(MediumText))
    source_website: str
    created_at: str = Field(default_factory=get_current_time)
    analyzed: bool = False
    org_id: str = Field(default="default", nullable=True)

# Add Website model
class Website(SQLModel, table=True):
    __tablename__ = 'websites_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    url: str = Field(index=True)
    name: str
    created_at: str = Field(default_factory=get_current_time)
    last_updated: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)

# Enhanced PolicyAnalysis model for backup policy analysis workflow
class PolicyAnalysis(SQLModel, table=True):
    __tablename__ = 'policy_analysis_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    # Scrape request reference ID as sole tracking mechanism
    scrape_request_ref_id: str = Field(index=True, unique=True)
    website: str = Field(default="", nullable=False)  # Original website URL

    # Classification results
    hard_classification_results: str = Field(default="", sa_column=Column(MediumText))
    soft_classification_results: str = Field(default="", sa_column=Column(MediumText))
    final_classification_used: str = Field(default="soft")  # "hard" or "soft"

    # Legacy fields for backward compatibility
    categorized_urls: str = Field(default="", sa_column=Column(MediumText))
    policy_details: str = Field(default="", sa_column=Column(MediumText))

    # Workflow tracking
    classification_status: str = Field(default="PENDING")  # PENDING, HARD_COMPLETED, SOFT_COMPLETED, FAILED
    screenshot_status: str = Field(default="PENDING")  # PENDING, IN_PROGRESS, COMPLETED, FAILED
    processing_status: str = Field(default="PENDING")  # PENDING, PROCESSING, COMPLETED, FAILED

    # Timestamps
    created_at: str = Field(default_factory=get_current_time)
    started_at: Optional[str] = Field(default=None, nullable=True)
    classification_completed_at: Optional[str] = Field(default=None, nullable=True)
    screenshot_completed_at: Optional[str] = Field(default=None, nullable=True)
    completed_at: Optional[str] = Field(default=None, nullable=True)

    # Error handling
    error_message: Optional[str] = Field(default=None, nullable=True)
    classification_error: Optional[str] = Field(default=None, nullable=True)
    screenshot_error: Optional[str] = Field(default=None, nullable=True)

    # Required org_id field
    org_id: str = Field(nullable=False, index=True)  # Mandatory org_id persistence

    # Metrics
    total_urls_processed: int = Field(default=0)
    screenshots_captured: int = Field(default=0)
    priority_urls_count: int = Field(default=0)

# Enhanced PolicyScreenshots model for priority-based screenshot management
class PolicyScreenshots(SQLModel, table=True):
    __tablename__ = 'policy_screenshots_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    policy_analysis_id: int = Field(index=True)  # References PolicyAnalysis.id
    scrape_request_ref_id: str = Field(index=True, nullable=False)  # Primary tracking identifier

    # URL and classification information
    original_url: str = Field(sa_column=Column(MediumText, nullable=False))
    url_category: str = Field(nullable=False)  # home_page, about_us, etc.
    url_depth: int = Field(default=1)
    priority_score: int = Field(default=0)  # Priority ranking for screenshot capture

    # Screenshot information
    screenshot_url: str = Field(default="", sa_column=Column(MediumText))  # Azure blob URL
    azure_filename: Optional[str] = Field(default=None, nullable=True)
    azure_container: str = Field(default="screenshots-dsapi")

    # Capture status and attempts
    capture_status: str = Field(default="PENDING")  # PENDING, IN_PROGRESS, CAPTURED, FAILED, SKIPPED
    capture_attempts: int = Field(default=0)
    max_retry_attempts: int = Field(default=3)

    # File metadata
    file_size_bytes: Optional[int] = Field(default=None, nullable=True)
    image_width: Optional[int] = Field(default=None, nullable=True)
    image_height: Optional[int] = Field(default=None, nullable=True)

    # Timestamps
    created_at: str = Field(default_factory=get_current_time)
    capture_started_at: Optional[str] = Field(default=None, nullable=True)
    captured_at: Optional[str] = Field(default=None, nullable=True)
    last_attempt_at: Optional[str] = Field(default=None, nullable=True)

    # Error handling
    error_message: Optional[str] = Field(default=None, nullable=True)
    last_error: Optional[str] = Field(default=None, nullable=True)

    # Required org_id field
    org_id: str = Field(nullable=False, index=True)  # Mandatory org_id persistence

# Add MCC URL Classification model for detailed URL classification tracking
class MccUrlClassification(SQLModel, table=True):
    __tablename__ = 'mcc_url_classification_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    mcc_analysis_id: int = Field(index=True)  # References MccAnalysis.id
    scrape_request_ref_id: str = Field(index=True)
    url: str = Field(sa_column=Column(MediumText))
    url_depth: int = Field(default=1)
    soft_classification: str = ""  # Category from soft classification
    hard_classification: str = ""  # Category from hard classification
    final_classification: str = ""  # Final assigned category
    is_priority_url: bool = Field(default=False)
    is_social_media: bool = Field(default=False)
    created_at: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)

# Enhanced PolicyUrlClassification model for two-tier classification tracking
class PolicyUrlClassification(SQLModel, table=True):
    __tablename__ = 'policy_url_classification_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    policy_analysis_id: int = Field(index=True)  # References PolicyAnalysis.id
    scrape_request_ref_id: str = Field(index=True, nullable=False)  # Primary tracking identifier

    # URL information
    url: str = Field(sa_column=Column(MediumText, nullable=False))
    url_depth: int = Field(default=1)
    url_domain: str = Field(default="")

    # Two-tier classification system
    hard_classification: str = Field(default="")  # Primary classification attempt
    hard_classification_status: str = Field(default="PENDING")  # PENDING, COMPLETED, FAILED
    hard_classification_error: Optional[str] = Field(default=None, nullable=True)

    soft_classification: str = Field(default="")  # Fallback classification
    soft_classification_status: str = Field(default="PENDING")  # PENDING, COMPLETED, FAILED
    soft_classification_error: Optional[str] = Field(default=None, nullable=True)

    # Final classification result (used for subsequent processing)
    final_classification: str = Field(default="")  # Final assigned category
    classification_source: str = Field(default="")  # "hard" or "soft" - which classification was used

    # URL categorization
    is_priority_url: bool = Field(default=False)
    is_social_media: bool = Field(default=False)
    is_excluded: bool = Field(default=False)
    priority_score: int = Field(default=0)  # For screenshot priority ordering

    # Processing timestamps
    created_at: str = Field(default_factory=get_current_time)
    hard_classification_at: Optional[str] = Field(default=None, nullable=True)
    soft_classification_at: Optional[str] = Field(default=None, nullable=True)
    final_classification_at: Optional[str] = Field(default=None, nullable=True)

    # Required org_id field
    org_id: str = Field(nullable=False, index=True)  # Mandatory org_id persistence

# Add SocialMediaAnalysis model
class SocialMediaAnalysis(SQLModel, table=True):
    __tablename__ = 'social_media_analysis_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    website: str
    scrape_request_ref_id: str = Field(index=True)
    social_media_data: str = Field(sa_column=Column(MediumText))
    analysis_summary: str = Field(sa_column=Column(MediumText))
    detailed_analysis: str = Field(sa_column=Column(MediumText))
    has_issues: bool = False
    created_at: str = Field(default_factory=get_current_time)
    started_at: Optional[str] = Field(default=None, nullable=True)
    completed_at: Optional[str] = Field(default=None, nullable=True)
    processing_status: str = "PENDING"  # PENDING, PROCESSING, COMPLETED, FAILED
    error_message: Optional[str] = Field(default=None, nullable=True)
    org_id: str = Field(default="default", nullable=True)


class PolicyAnalysisNew(SQLModel, table=True):
    """
    New Policy Analysis model - stores policy analysis results following MCC service patterns
    """
    __tablename__ = 'policy_analysis_new_gemini'
    __table_args__ = {'extend_existing': True}  # Force table refresh

    id: Optional[int] = Field(default=None, primary_key=True)
    website: str
    scrape_request_ref_id: str = Field(index=True)

    # Analysis flow tracking (similar to RiskyAnalysis)
    analysis_flow_used: Optional[str] = Field(default="normal", nullable=True)  # "normal" or "backup"
    reachability_percentage: Optional[float] = Field(default=None, nullable=True)
    total_urls_processed: Optional[int] = Field(default=0, nullable=True)

    # Policy URLs and content
    # Home page content (main website)
    home_page_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    home_page_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    home_page_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    returns_cancellation_exchange_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    returns_cancellation_exchange_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    returns_cancellation_exchange_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    privacy_policy_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    privacy_policy_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    privacy_policy_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    terms_and_condition_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    terms_and_condition_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    terms_and_condition_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    shipping_delivery_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    shipping_delivery_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    shipping_delivery_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    contact_us_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    contact_us_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    contact_us_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    about_us_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    about_us_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    about_us_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Social media URLs and content
    instagram_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    instagram_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    instagram_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    youtube_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    youtube_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    youtube_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    facebook_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    facebook_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    facebook_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    twitter_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    twitter_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    twitter_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    linkedin_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    linkedin_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    linkedin_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    pinterest_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    pinterest_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    pinterest_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    x_url: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    x_text: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    x_screenshot: Optional[str] = Field(default=None, sa_column=Column(MediumText))

    # Status and metadata fields following MCC pattern
    result_status: Optional[str] = Field(default="PENDING")
    created_at: Optional[str] = Field(default_factory=get_current_time)
    started_at: Optional[str] = Field(default=None)
    completed_at: Optional[str] = Field(default=None)
    failed_at: Optional[str] = Field(default=None)
    last_updated: Optional[str] = Field(default=None)
    error_message: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    details: Optional[str] = Field(default=None, sa_column=Column(MediumText))
    org_id: str = Field(default="default")
    processing_status: str = Field(default="PENDING")  # PENDING, PROCESSING, COMPLETED, FAILED

# Add SocialMediaProfileResult model for storing individual profile results
class SocialMediaProfileResult(SQLModel, table=True):
    __tablename__ = 'social_media_profile_result_gemini'
    id: Optional[int] = Field(default=None, primary_key=True)
    analysis_id: int = Field(index=True)  # References SocialMediaAnalysis.id
    platform: str  # Instagram, Facebook, Twitter, etc.
    profile_url: str = Field(sa_column=Column(MediumText))
    analysis_result: str = Field(sa_column=Column(MediumText))
    has_issues: bool = False
    issues_found: str = Field(default="", sa_column=Column(MediumText))
    created_at: str = Field(default_factory=get_current_time)
    org_id: str = Field(default="default", nullable=True)


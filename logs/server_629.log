nohup: ignoring input
INFO:     Started server process [500266]
INFO:     Waiting for application startup.
2025-07-29 17:35:08,411 [app.main] INFO: Initializing application
2025-07-29 17:35:08,416 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-07-29 17:35:08,416 [sqlalchemy.engine.Engine] INFO: SELECT DATABASE()
2025-07-29 17:35:08,416 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,416 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,417 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-07-29 17:35:08,417 [sqlalchemy.engine.Engine] INFO: SELECT @@sql_mode
2025-07-29 17:35:08,418 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,418 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,418 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-07-29 17:35:08,418 [sqlalchemy.engine.Engine] INFO: SELECT @@lower_case_table_names
2025-07-29 17:35:08,418 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,418 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,419 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:35:08,419 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:35:08,419 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_analysis_gemini`
2025-07-29 17:35:08,419 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`mcc_analysis_gemini`
2025-07-29 17:35:08,419 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,419 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,424 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-29 17:35:08,424 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-29 17:35:08,424 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,424 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,427 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-29 17:35:08,427 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-29 17:35:08,427 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,427 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,430 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`gemini_api_log_gemini`
2025-07-29 17:35:08,430 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`gemini_api_log_gemini`
2025-07-29 17:35:08,430 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,430 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,432 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`risky_analysis_gemini`
2025-07-29 17:35:08,432 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`risky_analysis_gemini`
2025-07-29 17:35:08,432 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,432 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,434 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`risky_url_analysis_gemini`
2025-07-29 17:35:08,434 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`risky_url_analysis_gemini`
2025-07-29 17:35:08,434 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,434 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,436 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`risky_keyword_result_gemini`
2025-07-29 17:35:08,436 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`risky_keyword_result_gemini`
2025-07-29 17:35:08,436 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,436 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,437 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`scrape_request_tracker_gemini`
2025-07-29 17:35:08,437 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`scrape_request_tracker_gemini`
2025-07-29 17:35:08,437 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,437 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,439 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`scraped_urls_gemini`
2025-07-29 17:35:08,439 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`scraped_urls_gemini`
2025-07-29 17:35:08,439 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,439 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,440 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`websites_gemini`
2025-07-29 17:35:08,440 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`websites_gemini`
2025-07-29 17:35:08,440 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,440 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,442 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_gemini`
2025-07-29 17:35:08,442 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`policy_analysis_gemini`
2025-07-29 17:35:08,442 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,442 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,443 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_screenshots_gemini`
2025-07-29 17:35:08,443 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`policy_screenshots_gemini`
2025-07-29 17:35:08,443 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,443 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,445 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-29 17:35:08,445 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-29 17:35:08,445 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,445 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,446 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-29 17:35:08,446 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-29 17:35:08,446 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,446 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,448 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`social_media_analysis_gemini`
2025-07-29 17:35:08,448 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`social_media_analysis_gemini`
2025-07-29 17:35:08,448 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,448 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,449 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-29 17:35:08,449 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-29 17:35:08,449 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,449 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,451 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`social_media_profile_result_gemini`
2025-07-29 17:35:08,451 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`social_media_profile_result_gemini`
2025-07-29 17:35:08,451 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-29 17:35:08,451 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-29 17:35:08,453 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:35:08,453 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:35:08,454 [app.main] INFO: Database initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
2025-07-29 17:36:16,895 [app.routers.mcc_analysis] INFO: Processing MCC analysis request for https://www.gogateproducts.com with ref_id 1d5434d5-a4eb-4f3a-9a38-7deb506d0b90
2025-07-29 17:36:16,898 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:36:16,898 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:36:16,924 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:16,924 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:16,924 INFO sqlalchemy.engine.Engine [generated in 0.00025s] {'scrape_request_ref_id_1': '1d5434d5-a4eb-4f3a-9a38-7deb506d0b90'}
2025-07-29 17:36:16,924 [sqlalchemy.engine.Engine] INFO: [generated in 0.00025s] {'scrape_request_ref_id_1': '1d5434d5-a4eb-4f3a-9a38-7deb506d0b90'}
2025-07-29 17:36:16,935 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:16,935 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:16,935 INFO sqlalchemy.engine.Engine [generated in 0.00016s] {'scrape_request_ref_id_1': '1d5434d5-a4eb-4f3a-9a38-7deb506d0b90'}
2025-07-29 17:36:16,935 [sqlalchemy.engine.Engine] INFO: [generated in 0.00016s] {'scrape_request_ref_id_1': '1d5434d5-a4eb-4f3a-9a38-7deb506d0b90'}
2025-07-29 17:36:16,937 [app.utils.website_url_processor] WARNING: No parsed_urls found in request
2025-07-29 17:36:16,937 [app.routers.mcc_analysis] ERROR: Failed to store URLs
2025-07-29 17:36:16,937 [app.routers.mcc_analysis] ERROR: Error storing URLs: 500: Failed to store website URLs
2025-07-29 17:36:16,937 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:36:16,937 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:44106 - "POST /mcc-analysis/ HTTP/1.1" 500 Internal Server Error
2025-07-29 17:36:16,963 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: 1d5434d5-a4eb-4f3a-9a38-7deb506d0b90
2025-07-29 17:36:16,964 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:36:16,964 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:36:16,964 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:16,964 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:16,964 INFO sqlalchemy.engine.Engine [cached since 0.02913s ago] {'scrape_request_ref_id_1': '1d5434d5-a4eb-4f3a-9a38-7deb506d0b90'}
2025-07-29 17:36:16,964 [sqlalchemy.engine.Engine] INFO: [cached since 0.02913s ago] {'scrape_request_ref_id_1': '1d5434d5-a4eb-4f3a-9a38-7deb506d0b90'}
2025-07-29 17:36:16,965 [app.utils.website_url_processor] WARNING: No parsed_urls found in request
2025-07-29 17:36:16,965 [app.routers.policy_analysis] ERROR: Error queuing policy analysis: 500: Failed to store website URLs
2025-07-29 17:36:16,966 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:36:16,966 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:44106 - "POST /policy-analysis/ HTTP/1.1" 500 Internal Server Error
INFO:     *************:44106 - "POST /risky-classification/ HTTP/1.1" 404 Not Found
2025-07-29 17:36:20,467 [app.routers.mcc_analysis] INFO: Processing MCC analysis request for https://www.coinbase.com/en-gb/ with ref_id 26553ae2-8ad1-4328-8887-4b8dca905d8f
2025-07-29 17:36:20,468 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:36:20,468 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:36:20,468 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:20,468 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:20,468 INFO sqlalchemy.engine.Engine [cached since 3.545s ago] {'scrape_request_ref_id_1': '26553ae2-8ad1-4328-8887-4b8dca905d8f'}
2025-07-29 17:36:20,468 [sqlalchemy.engine.Engine] INFO: [cached since 3.545s ago] {'scrape_request_ref_id_1': '26553ae2-8ad1-4328-8887-4b8dca905d8f'}
2025-07-29 17:36:20,473 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:20,473 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:20,473 INFO sqlalchemy.engine.Engine [cached since 3.538s ago] {'scrape_request_ref_id_1': '26553ae2-8ad1-4328-8887-4b8dca905d8f'}
2025-07-29 17:36:20,473 [sqlalchemy.engine.Engine] INFO: [cached since 3.538s ago] {'scrape_request_ref_id_1': '26553ae2-8ad1-4328-8887-4b8dca905d8f'}
2025-07-29 17:36:20,476 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:36:20,476 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:36:20,476 INFO sqlalchemy.engine.Engine [generated in 0.00022s] {'scrape_request_ref_id': '26553ae2-8ad1-4328-8887-4b8dca905d8f', 'website': 'https://www.coinbase.com/en-gb/', 'url': 'https://www.coinbase.com/en-gb/', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '2'}
2025-07-29 17:36:20,476 [sqlalchemy.engine.Engine] INFO: [generated in 0.00022s] {'scrape_request_ref_id': '26553ae2-8ad1-4328-8887-4b8dca905d8f', 'website': 'https://www.coinbase.com/en-gb/', 'url': 'https://www.coinbase.com/en-gb/', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '2'}
2025-07-29 17:36:20,477 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:36:20,477 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:36:20,480 [app.utils.website_url_processor] INFO: Successfully stored 1 URLs for scrape_request_ref_id: 26553ae2-8ad1-4328-8887-4b8dca905d8f
2025-07-29 17:36:20,481 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:36:20,481 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:36:20,482 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:36:20,482 [sqlalchemy.engine.Engine] INFO: INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:36:20,482 INFO sqlalchemy.engine.Engine [generated in 0.00021s] {'website': 'https://www.coinbase.com/en-gb/', 'scrape_request_ref_id': '26553ae2-8ad1-4328-8887-4b8dca905d8f', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T17:36:20.480781Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '2', 'processing_status': 'PENDING'}
2025-07-29 17:36:20,482 [sqlalchemy.engine.Engine] INFO: [generated in 0.00021s] {'website': 'https://www.coinbase.com/en-gb/', 'scrape_request_ref_id': '26553ae2-8ad1-4328-8887-4b8dca905d8f', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T17:36:20.480781Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '2', 'processing_status': 'PENDING'}
2025-07-29 17:36:20,483 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:36:20,483 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:36:20,486 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:36:20,486 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:36:20,488 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 17:36:20,488 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 17:36:20,488 INFO sqlalchemy.engine.Engine [generated in 0.00017s] {'pk_1': 1612}
2025-07-29 17:36:20,488 [sqlalchemy.engine.Engine] INFO: [generated in 0.00017s] {'pk_1': 1612}
2025-07-29 17:36:20,489 [app.routers.mcc_analysis] INFO: Created new MCC analysis with ID 1612
2025-07-29 17:36:20,544 [app.routers.mcc_analysis] INFO: Triggered Celery task e6690a3d-3d41-46d0-82bb-6f27461dfc3c for analysis 1612
2025-07-29 17:36:20,545 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:36:20,545 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:44106 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-29 17:36:20,554 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: 26553ae2-8ad1-4328-8887-4b8dca905d8f
2025-07-29 17:36:20,555 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:36:20,555 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:36:20,556 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:20,556 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:36:20,556 INFO sqlalchemy.engine.Engine [cached since 3.621s ago] {'scrape_request_ref_id_1': '26553ae2-8ad1-4328-8887-4b8dca905d8f'}
2025-07-29 17:36:20,556 [sqlalchemy.engine.Engine] INFO: [cached since 3.621s ago] {'scrape_request_ref_id_1': '26553ae2-8ad1-4328-8887-4b8dca905d8f'}
2025-07-29 17:36:20,557 [app.utils.website_url_processor] INFO: URLs already exist for scrape_request_ref_id: 26553ae2-8ad1-4328-8887-4b8dca905d8f, count: 1
2025-07-29 17:36:20,561 INFO sqlalchemy.engine.Engine INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:36:20,561 [sqlalchemy.engine.Engine] INFO: INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:36:20,562 INFO sqlalchemy.engine.Engine [generated in 0.00032s] {'website': 'https://www.coinbase.com/en-gb/', 'scrape_request_ref_id': '26553ae2-8ad1-4328-8887-4b8dca905d8f', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T17:36:20.557609Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '2', 'processing_status': 'PENDING'}
2025-07-29 17:36:20,562 [sqlalchemy.engine.Engine] INFO: [generated in 0.00032s] {'website': 'https://www.coinbase.com/en-gb/', 'scrape_request_ref_id': '26553ae2-8ad1-4328-8887-4b8dca905d8f', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T17:36:20.557609Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '2', 'processing_status': 'PENDING'}
2025-07-29 17:36:20,563 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:36:20,563 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:36:20,566 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:36:20,566 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:36:20,568 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 17:36:20,568 [sqlalchemy.engine.Engine] INFO: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 17:36:20,569 INFO sqlalchemy.engine.Engine [generated in 0.00015s] {'pk_1': 1076}
2025-07-29 17:36:20,569 [sqlalchemy.engine.Engine] INFO: [generated in 0.00015s] {'pk_1': 1076}
2025-07-29 17:36:20,570 [app.routers.policy_analysis] INFO: Queuing enhanced policy analysis task for analysis_id: 1076
2025-07-29 17:36:20,582 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:36:20,582 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:44106 - "POST /policy-analysis/ HTTP/1.1" 200 OK
INFO:     *************:44106 - "POST /risky-classification/ HTTP/1.1" 404 Not Found
2025-07-29 17:43:28,425 [app.routers.mcc_analysis] INFO: Processing MCC analysis request for https://www.thewhiskyexchange.com/ with ref_id 1d33f0e7-c842-422e-b3b6-6e9c761068a8
2025-07-29 17:43:28,426 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:43:28,426 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:43:28,427 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:43:28,427 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:43:28,427 INFO sqlalchemy.engine.Engine [cached since 431.5s ago] {'scrape_request_ref_id_1': '1d33f0e7-c842-422e-b3b6-6e9c761068a8'}
2025-07-29 17:43:28,427 [sqlalchemy.engine.Engine] INFO: [cached since 431.5s ago] {'scrape_request_ref_id_1': '1d33f0e7-c842-422e-b3b6-6e9c761068a8'}
2025-07-29 17:43:28,433 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:43:28,433 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:43:28,433 INFO sqlalchemy.engine.Engine [cached since 431.5s ago] {'scrape_request_ref_id_1': '1d33f0e7-c842-422e-b3b6-6e9c761068a8'}
2025-07-29 17:43:28,433 [sqlalchemy.engine.Engine] INFO: [cached since 431.5s ago] {'scrape_request_ref_id_1': '1d33f0e7-c842-422e-b3b6-6e9c761068a8'}
2025-07-29 17:43:28,434 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:43:28,434 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:43:28,434 INFO sqlalchemy.engine.Engine [cached since 428s ago] {'scrape_request_ref_id': '1d33f0e7-c842-422e-b3b6-6e9c761068a8', 'website': 'https://www.thewhiskyexchange.com/', 'url': 'https://www.thewhiskyexchange.com/', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:43:28,434 [sqlalchemy.engine.Engine] INFO: [cached since 428s ago] {'scrape_request_ref_id': '1d33f0e7-c842-422e-b3b6-6e9c761068a8', 'website': 'https://www.thewhiskyexchange.com/', 'url': 'https://www.thewhiskyexchange.com/', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:43:28,435 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:43:28,435 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:43:28,438 [app.utils.website_url_processor] INFO: Successfully stored 1 URLs for scrape_request_ref_id: 1d33f0e7-c842-422e-b3b6-6e9c761068a8
2025-07-29 17:43:28,438 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:43:28,438 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:43:28,439 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:43:28,439 [sqlalchemy.engine.Engine] INFO: INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:43:28,439 INFO sqlalchemy.engine.Engine [cached since 428s ago] {'website': 'https://www.thewhiskyexchange.com/', 'scrape_request_ref_id': '1d33f0e7-c842-422e-b3b6-6e9c761068a8', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T17:43:28.438417Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:43:28,439 [sqlalchemy.engine.Engine] INFO: [cached since 428s ago] {'website': 'https://www.thewhiskyexchange.com/', 'scrape_request_ref_id': '1d33f0e7-c842-422e-b3b6-6e9c761068a8', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T17:43:28.438417Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:43:28,439 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:43:28,439 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:43:28,442 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:43:28,442 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:43:28,443 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 17:43:28,443 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 17:43:28,443 INFO sqlalchemy.engine.Engine [cached since 428s ago] {'pk_1': 1613}
2025-07-29 17:43:28,443 [sqlalchemy.engine.Engine] INFO: [cached since 428s ago] {'pk_1': 1613}
2025-07-29 17:43:28,444 [app.routers.mcc_analysis] INFO: Created new MCC analysis with ID 1613
2025-07-29 17:43:28,445 [app.routers.mcc_analysis] INFO: Triggered Celery task 20b9615b-218e-42b9-8703-be294eb88211 for analysis 1613
2025-07-29 17:43:28,445 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:43:28,445 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:46840 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-29 17:43:28,456 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: 1d33f0e7-c842-422e-b3b6-6e9c761068a8
2025-07-29 17:43:28,457 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:43:28,457 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:43:28,457 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:43:28,457 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:43:28,457 INFO sqlalchemy.engine.Engine [cached since 431.5s ago] {'scrape_request_ref_id_1': '1d33f0e7-c842-422e-b3b6-6e9c761068a8'}
2025-07-29 17:43:28,457 [sqlalchemy.engine.Engine] INFO: [cached since 431.5s ago] {'scrape_request_ref_id_1': '1d33f0e7-c842-422e-b3b6-6e9c761068a8'}
2025-07-29 17:43:28,458 [app.utils.website_url_processor] INFO: URLs already exist for scrape_request_ref_id: 1d33f0e7-c842-422e-b3b6-6e9c761068a8, count: 1
2025-07-29 17:43:28,459 INFO sqlalchemy.engine.Engine INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:43:28,459 [sqlalchemy.engine.Engine] INFO: INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:43:28,459 INFO sqlalchemy.engine.Engine [cached since 427.9s ago] {'website': 'https://www.thewhiskyexchange.com/', 'scrape_request_ref_id': '1d33f0e7-c842-422e-b3b6-6e9c761068a8', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T17:43:28.459045Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:43:28,459 [sqlalchemy.engine.Engine] INFO: [cached since 427.9s ago] {'website': 'https://www.thewhiskyexchange.com/', 'scrape_request_ref_id': '1d33f0e7-c842-422e-b3b6-6e9c761068a8', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T17:43:28.459045Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:43:28,460 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:43:28,460 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:43:28,466 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:43:28,466 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:43:28,466 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 17:43:28,466 [sqlalchemy.engine.Engine] INFO: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 17:43:28,466 INFO sqlalchemy.engine.Engine [cached since 427.9s ago] {'pk_1': 1077}
2025-07-29 17:43:28,466 [sqlalchemy.engine.Engine] INFO: [cached since 427.9s ago] {'pk_1': 1077}
2025-07-29 17:43:28,468 [app.routers.policy_analysis] INFO: Queuing enhanced policy analysis task for analysis_id: 1077
2025-07-29 17:43:28,469 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:43:28,469 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:46840 - "POST /policy-analysis/ HTTP/1.1" 200 OK
2025-07-29 17:44:01,233 [app.routers.mcc_analysis] INFO: Processing MCC analysis request for https://www.gotoliquorstore.com/ with ref_id a00f808b-4d18-4ba0-8210-0a7144d8edd4
2025-07-29 17:44:01,236 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:44:01,236 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:44:01,236 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:01,236 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:01,236 INFO sqlalchemy.engine.Engine [cached since 464.3s ago] {'scrape_request_ref_id_1': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4'}
2025-07-29 17:44:01,236 [sqlalchemy.engine.Engine] INFO: [cached since 464.3s ago] {'scrape_request_ref_id_1': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4'}
2025-07-29 17:44:01,241 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:01,241 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:01,241 INFO sqlalchemy.engine.Engine [cached since 464.3s ago] {'scrape_request_ref_id_1': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4'}
2025-07-29 17:44:01,241 [sqlalchemy.engine.Engine] INFO: [cached since 464.3s ago] {'scrape_request_ref_id_1': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4'}
2025-07-29 17:44:01,243 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:44:01,243 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:44:01,243 INFO sqlalchemy.engine.Engine [cached since 460.8s ago] {'scrape_request_ref_id': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4', 'website': 'https://www.gotoliquorstore.com/', 'url': 'https://www.gotoliquorstore.com/', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:44:01,243 [sqlalchemy.engine.Engine] INFO: [cached since 460.8s ago] {'scrape_request_ref_id': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4', 'website': 'https://www.gotoliquorstore.com/', 'url': 'https://www.gotoliquorstore.com/', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:44:01,243 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:44:01,243 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:44:01,247 [app.utils.website_url_processor] INFO: Successfully stored 1 URLs for scrape_request_ref_id: a00f808b-4d18-4ba0-8210-0a7144d8edd4
2025-07-29 17:44:01,247 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:44:01,247 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:44:01,248 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:44:01,248 [sqlalchemy.engine.Engine] INFO: INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:44:01,248 INFO sqlalchemy.engine.Engine [cached since 460.8s ago] {'website': 'https://www.gotoliquorstore.com/', 'scrape_request_ref_id': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T17:44:01.247317Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:44:01,248 [sqlalchemy.engine.Engine] INFO: [cached since 460.8s ago] {'website': 'https://www.gotoliquorstore.com/', 'scrape_request_ref_id': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T17:44:01.247317Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:44:01,248 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:44:01,248 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:44:01,252 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:44:01,252 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:44:01,252 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 17:44:01,252 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 17:44:01,252 INFO sqlalchemy.engine.Engine [cached since 460.8s ago] {'pk_1': 1614}
2025-07-29 17:44:01,252 [sqlalchemy.engine.Engine] INFO: [cached since 460.8s ago] {'pk_1': 1614}
2025-07-29 17:44:01,253 [app.routers.mcc_analysis] INFO: Created new MCC analysis with ID 1614
2025-07-29 17:44:01,254 [app.routers.mcc_analysis] INFO: Triggered Celery task 6eebe121-a274-493f-8d47-cd6b76f5dbd3 for analysis 1614
2025-07-29 17:44:01,255 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:44:01,255 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:32808 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-29 17:44:01,265 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: a00f808b-4d18-4ba0-8210-0a7144d8edd4
2025-07-29 17:44:01,265 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:44:01,265 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:44:01,265 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:01,265 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:01,265 INFO sqlalchemy.engine.Engine [cached since 464.3s ago] {'scrape_request_ref_id_1': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4'}
2025-07-29 17:44:01,265 [sqlalchemy.engine.Engine] INFO: [cached since 464.3s ago] {'scrape_request_ref_id_1': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4'}
2025-07-29 17:44:01,267 [app.utils.website_url_processor] INFO: URLs already exist for scrape_request_ref_id: a00f808b-4d18-4ba0-8210-0a7144d8edd4, count: 1
2025-07-29 17:44:01,267 INFO sqlalchemy.engine.Engine INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:44:01,267 [sqlalchemy.engine.Engine] INFO: INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:44:01,267 INFO sqlalchemy.engine.Engine [cached since 460.7s ago] {'website': 'https://www.gotoliquorstore.com/', 'scrape_request_ref_id': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T17:44:01.267075Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:44:01,267 [sqlalchemy.engine.Engine] INFO: [cached since 460.7s ago] {'website': 'https://www.gotoliquorstore.com/', 'scrape_request_ref_id': 'a00f808b-4d18-4ba0-8210-0a7144d8edd4', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T17:44:01.267075Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:44:01,268 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:44:01,268 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:44:01,273 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:44:01,273 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:44:01,273 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 17:44:01,273 [sqlalchemy.engine.Engine] INFO: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 17:44:01,273 INFO sqlalchemy.engine.Engine [cached since 460.7s ago] {'pk_1': 1078}
2025-07-29 17:44:01,273 [sqlalchemy.engine.Engine] INFO: [cached since 460.7s ago] {'pk_1': 1078}
2025-07-29 17:44:01,275 [app.routers.policy_analysis] INFO: Queuing enhanced policy analysis task for analysis_id: 1078
2025-07-29 17:44:01,276 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:44:01,276 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:32808 - "POST /policy-analysis/ HTTP/1.1" 200 OK
2025-07-29 17:44:47,249 [app.routers.mcc_analysis] INFO: Processing MCC analysis request for https://www.gogateproducts.com with ref_id 2530a72d-5c3d-4d9c-9c52-d7829f816534
2025-07-29 17:44:47,251 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:44:47,251 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:44:47,251 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:47,251 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:47,252 INFO sqlalchemy.engine.Engine [cached since 510.3s ago] {'scrape_request_ref_id_1': '2530a72d-5c3d-4d9c-9c52-d7829f816534'}
2025-07-29 17:44:47,252 [sqlalchemy.engine.Engine] INFO: [cached since 510.3s ago] {'scrape_request_ref_id_1': '2530a72d-5c3d-4d9c-9c52-d7829f816534'}
2025-07-29 17:44:47,257 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:47,257 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:47,257 INFO sqlalchemy.engine.Engine [cached since 510.3s ago] {'scrape_request_ref_id_1': '2530a72d-5c3d-4d9c-9c52-d7829f816534'}
2025-07-29 17:44:47,257 [sqlalchemy.engine.Engine] INFO: [cached since 510.3s ago] {'scrape_request_ref_id_1': '2530a72d-5c3d-4d9c-9c52-d7829f816534'}
2025-07-29 17:44:47,258 [app.utils.website_url_processor] WARNING: No parsed_urls found in request
2025-07-29 17:44:47,258 [app.routers.mcc_analysis] ERROR: Failed to store URLs
2025-07-29 17:44:47,258 [app.routers.mcc_analysis] ERROR: Error storing URLs: 500: Failed to store website URLs
2025-07-29 17:44:47,258 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:44:47,258 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:44882 - "POST /mcc-analysis/ HTTP/1.1" 500 Internal Server Error
2025-07-29 17:44:47,268 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: 2530a72d-5c3d-4d9c-9c52-d7829f816534
2025-07-29 17:44:47,269 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:44:47,269 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:44:47,269 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:47,269 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:44:47,269 INFO sqlalchemy.engine.Engine [cached since 510.3s ago] {'scrape_request_ref_id_1': '2530a72d-5c3d-4d9c-9c52-d7829f816534'}
2025-07-29 17:44:47,269 [sqlalchemy.engine.Engine] INFO: [cached since 510.3s ago] {'scrape_request_ref_id_1': '2530a72d-5c3d-4d9c-9c52-d7829f816534'}
2025-07-29 17:44:47,270 [app.utils.website_url_processor] WARNING: No parsed_urls found in request
2025-07-29 17:44:47,270 [app.routers.policy_analysis] ERROR: Error queuing policy analysis: 500: Failed to store website URLs
2025-07-29 17:44:47,270 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:44:47,270 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:44882 - "POST /policy-analysis/ HTTP/1.1" 500 Internal Server Error
2025-07-29 17:47:01,316 [app.routers.mcc_analysis] INFO: Processing MCC analysis request for https://artornate.com/ with ref_id 6833a80a-152a-43c0-889e-9f64d7540a48
2025-07-29 17:47:01,318 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:47:01,318 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:47:01,318 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:47:01,318 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:47:01,318 INFO sqlalchemy.engine.Engine [cached since 644.4s ago] {'scrape_request_ref_id_1': '6833a80a-152a-43c0-889e-9f64d7540a48'}
2025-07-29 17:47:01,318 [sqlalchemy.engine.Engine] INFO: [cached since 644.4s ago] {'scrape_request_ref_id_1': '6833a80a-152a-43c0-889e-9f64d7540a48'}
2025-07-29 17:47:01,324 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:47:01,324 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:47:01,324 INFO sqlalchemy.engine.Engine [cached since 644.4s ago] {'scrape_request_ref_id_1': '6833a80a-152a-43c0-889e-9f64d7540a48'}
2025-07-29 17:47:01,324 [sqlalchemy.engine.Engine] INFO: [cached since 644.4s ago] {'scrape_request_ref_id_1': '6833a80a-152a-43c0-889e-9f64d7540a48'}
2025-07-29 17:47:01,330 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,330 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,330 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,330 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,331 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,331 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,331 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/metallic-gold-insert-sheet', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,331 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/metallic-gold-insert-sheet', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,332 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,332 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,332 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/cart', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,332 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/cart', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,333 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,333 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,333 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/hexagon-striped-coaster-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,333 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/hexagon-striped-coaster-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,333 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,333 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,333 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/privacy-policy', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,333 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/privacy-policy', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,334 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,334 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,334 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin-stone', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,334 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin-stone', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,335 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,335 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,335 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/black-insert-sheet', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,335 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/black-insert-sheet', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,335 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,335 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,335 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin-pigments', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,335 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin-pigments', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,336 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,336 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,336 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/insert-sheet', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,336 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/insert-sheet', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,337 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,337 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,337 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,337 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,337 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,337 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,337 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/pages/contact', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,337 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/pages/contact', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,338 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,338 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,338 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/glitter-sequins', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,338 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/glitter-sequins', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,339 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,339 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,339 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/opaque-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,339 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/opaque-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,339 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,339 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,339 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/shipping-policy', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,339 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/shipping-policy', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,340 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,340 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,340 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,340 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,341 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,341 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,341 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://www.facebook.com/artornateofficial', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,341 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://www.facebook.com/artornateofficial', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,341 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,341 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,341 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/pearl-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,341 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/pearl-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,342 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,342 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,342 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/fluorescent-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,342 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/fluorescent-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,343 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,343 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,343 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,343 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,343 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,343 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,343 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/account/login', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,343 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/account/login', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,344 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,344 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,344 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/embossed-gold-metallic-foil-stickers', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,344 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/embossed-gold-metallic-foil-stickers', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,345 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,345 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,345 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/holographic-foil-stickers', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,345 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/holographic-foil-stickers', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,345 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,345 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,345 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/flower', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,345 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/flower', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,346 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,346 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,346 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/alcohol-ink', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,346 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/alcohol-ink', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,346 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,346 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,346 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/rakhi', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,346 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/rakhi', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,347 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,347 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,347 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/accessories', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,347 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/accessories', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,348 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,348 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,348 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/animal-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,348 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/animal-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,348 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,348 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,348 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/thank-you-stickers', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,348 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/thank-you-stickers', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,349 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,349 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,349 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/essentials', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,349 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/essentials', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,350 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,350 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,350 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/ocean-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,350 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/ocean-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,350 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,350 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,350 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/terms-of-service', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,350 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/terms-of-service', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,351 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,351 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,351 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/mdf-acrylic', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,351 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/mdf-acrylic', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,352 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,352 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,352 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/acrylic-cutout', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,352 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/acrylic-cutout', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,352 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,352 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,352 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/flakes', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,352 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/flakes', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,353 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,353 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,353 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://www.instagram.com/artornateofficial', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,353 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://www.instagram.com/artornateofficial', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,353 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,353 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,353 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/glow-in-dark-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,353 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/glow-in-dark-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,354 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,354 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,354 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/wpm@ad98e495we9015cc8p0f403f8ambaddb7f4/custom/web-pixel-shopify-custom-pixel@0440/sandbox/modern', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,354 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/wpm@ad98e495we9015cc8p0f403f8ambaddb7f4/custom/web-pixel-shopify-custom-pixel@0440/sandbox/modern', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,355 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,355 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,355 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/mica-powder-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,355 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/mica-powder-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,355 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,355 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,355 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/cosmic-paste-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,355 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/cosmic-paste-pigment', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,356 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,356 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,356 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/bezel', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,356 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/bezel', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,356 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,356 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,356 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin-tools', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,356 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin-tools', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,357 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,357 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,357 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/contact-information', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,357 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/contact-information', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,357 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,357 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,358 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin-diy-kit', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,358 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin-diy-kit', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,358 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,358 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,358 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/rainbow-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,358 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/rainbow-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,359 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,359 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,359 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/search', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,359 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/search', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,359 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,359 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,359 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/round-striped-coaster-silicone-mould-set-of-4-with-stand', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,359 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/round-striped-coaster-silicone-mould-set-of-4-with-stand', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,360 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,360 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,360 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/heart-love-message-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,360 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/heart-love-message-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,361 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,361 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/candy-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,361 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/candy-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,361 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,361 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,361 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/artificial-flower', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,361 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/artificial-flower', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,362 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,362 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,362 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/stands-and-handles', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,362 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/stands-and-handles', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,363 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,363 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,363 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/new-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,363 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/products/new-keychain-pendant-silicone-mould', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,363 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,363 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,363 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/mdf', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,363 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/mdf', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,364 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,364 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,364 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/cdn', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,364 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/cdn', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,365 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,365 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,365 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/all', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,365 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/all', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,365 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,365 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,365 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/refund-policy', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,365 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/policies/refund-policy', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,366 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,366 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,366 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://www.youtube.com/@Artornateofficial', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,366 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://www.youtube.com/@Artornateofficial', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,366 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,366 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,366 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/#MainContent', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,366 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/#MainContent', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,367 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,367 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,367 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/pressed-flower', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,367 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/pressed-flower', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,367 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,367 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 17:47:01,368 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,368 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'website': 'https://artornate.com/', 'url': 'https://artornate.com/collections/resin', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 17:47:01,368 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:47:01,368 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:47:01,372 [app.utils.website_url_processor] INFO: Successfully stored 59 URLs for scrape_request_ref_id: 6833a80a-152a-43c0-889e-9f64d7540a48
2025-07-29 17:47:01,372 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:47:01,372 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:47:01,372 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:47:01,372 [sqlalchemy.engine.Engine] INFO: INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:47:01,373 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'website': 'https://artornate.com/', 'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T17:47:01.372272Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:47:01,373 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'website': 'https://artornate.com/', 'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T17:47:01.372272Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:47:01,374 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:47:01,374 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:47:01,377 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:47:01,377 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:47:01,377 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 17:47:01,377 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 17:47:01,377 INFO sqlalchemy.engine.Engine [cached since 640.9s ago] {'pk_1': 1615}
2025-07-29 17:47:01,377 [sqlalchemy.engine.Engine] INFO: [cached since 640.9s ago] {'pk_1': 1615}
2025-07-29 17:47:01,378 [app.routers.mcc_analysis] INFO: Created new MCC analysis with ID 1615
2025-07-29 17:47:01,379 [app.routers.mcc_analysis] INFO: Triggered Celery task 8a2bab67-ed95-41a1-8578-059c4ae88d66 for analysis 1615
2025-07-29 17:47:01,380 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:47:01,380 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:36544 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-29 17:47:01,389 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: 6833a80a-152a-43c0-889e-9f64d7540a48
2025-07-29 17:47:01,390 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:47:01,390 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:47:01,390 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:47:01,390 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 17:47:01,390 INFO sqlalchemy.engine.Engine [cached since 644.5s ago] {'scrape_request_ref_id_1': '6833a80a-152a-43c0-889e-9f64d7540a48'}
2025-07-29 17:47:01,390 [sqlalchemy.engine.Engine] INFO: [cached since 644.5s ago] {'scrape_request_ref_id_1': '6833a80a-152a-43c0-889e-9f64d7540a48'}
2025-07-29 17:47:01,393 [app.utils.website_url_processor] INFO: URLs already exist for scrape_request_ref_id: 6833a80a-152a-43c0-889e-9f64d7540a48, count: 59
2025-07-29 17:47:01,393 INFO sqlalchemy.engine.Engine INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:47:01,393 [sqlalchemy.engine.Engine] INFO: INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 17:47:01,393 INFO sqlalchemy.engine.Engine [cached since 640.8s ago] {'website': 'https://artornate.com/', 'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T17:47:01.393184Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:47:01,393 [sqlalchemy.engine.Engine] INFO: [cached since 640.8s ago] {'website': 'https://artornate.com/', 'scrape_request_ref_id': '6833a80a-152a-43c0-889e-9f64d7540a48', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T17:47:01.393184Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 17:47:01,394 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:47:01,394 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:47:01,398 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:47:01,398 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:47:01,398 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 17:47:01,398 [sqlalchemy.engine.Engine] INFO: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 17:47:01,398 INFO sqlalchemy.engine.Engine [cached since 640.8s ago] {'pk_1': 1079}
2025-07-29 17:47:01,398 [sqlalchemy.engine.Engine] INFO: [cached since 640.8s ago] {'pk_1': 1079}
2025-07-29 17:47:01,400 [app.routers.policy_analysis] INFO: Queuing enhanced policy analysis task for analysis_id: 1079
2025-07-29 17:47:01,401 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:47:01,401 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:36544 - "POST /policy-analysis/ HTTP/1.1" 200 OK
2025-07-29 18:03:20,725 [app.routers.mcc_analysis] INFO: Processing MCC analysis request for https://aaaexports.com with ref_id f0aa52d4-41ca-43a6-8347-0dc710ebf1f1
2025-07-29 18:03:20,727 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 18:03:20,727 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 18:03:20,727 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:03:20,727 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:03:20,727 INFO sqlalchemy.engine.Engine [cached since 1624s ago] {'scrape_request_ref_id_1': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1'}
2025-07-29 18:03:20,727 [sqlalchemy.engine.Engine] INFO: [cached since 1624s ago] {'scrape_request_ref_id_1': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1'}
2025-07-29 18:03:20,732 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:03:20,732 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:03:20,733 INFO sqlalchemy.engine.Engine [cached since 1624s ago] {'scrape_request_ref_id_1': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1'}
2025-07-29 18:03:20,733 [sqlalchemy.engine.Engine] INFO: [cached since 1624s ago] {'scrape_request_ref_id_1': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1'}
2025-07-29 18:03:20,737 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,737 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,737 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,737 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,738 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,738 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,738 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/b23904/christmas-lights-in-palm-desert-b23904c-25-5064076-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,738 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/b23904/christmas-lights-in-palm-desert-b23904c-25-5064076-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,738 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,738 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,738 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/trader/angels-camp-urgent-care-b23904c-25-3809918-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,738 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/trader/angels-camp-urgent-care-b23904c-25-3809918-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,739 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,739 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,739 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/mens/used-luxury-watches-for-men-b23904c-25-3049212-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,739 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/mens/used-luxury-watches-for-men-b23904c-25-3049212-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,739 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,739 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,740 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/items/mens-pokemon-watch-b23904c-25-3091674-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,740 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/items/mens-pokemon-watch-b23904c-25-3091674-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,740 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,740 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,740 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/thread/antique-oil-lamp-green-glass-b23904c-25-2991520-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,740 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/thread/antique-oil-lamp-green-glass-b23904c-25-2991520-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,741 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,741 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,741 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/dealer/2024-husqvarna-350s-rear-wheel-spacers-b23904c-25-944212-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,741 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/dealer/2024-husqvarna-350s-rear-wheel-spacers-b23904c-25-944212-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,741 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,741 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,741 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/brand/cream-fitted-hat-new-era-b23904c-25-180088-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,741 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/brand/cream-fitted-hat-new-era-b23904c-25-180088-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,742 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,742 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,742 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/lgbt/chicago-bears-football-helmet-decals-b23904c-25-992706-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,742 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/lgbt/chicago-bears-football-helmet-decals-b23904c-25-992706-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,742 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,742 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,742 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sellers/pen-surveillance-cameras-b23904c-25-4643420-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,742 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sellers/pen-surveillance-cameras-b23904c-25-4643420-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,743 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,743 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,743 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://www.facebook.com/aaaexportscom', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,743 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://www.facebook.com/aaaexportscom', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,743 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,743 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,744 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://twitter.com/aaaexportscom', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,744 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://twitter.com/aaaexportscom', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,744 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,744 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,744 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sales/net-mask-truss-b23904c-25-5408788-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,744 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sales/net-mask-truss-b23904c-25-5408788-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,745 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,745 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,745 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/unisex/belted-pocket-bag-b23904c-25-2362822-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,745 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/unisex/belted-pocket-bag-b23904c-25-2362822-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,745 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,745 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,745 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/deals/ladybug-yoga-camp-b23904c-25-6215934-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,745 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/deals/ladybug-yoga-camp-b23904c-25-6215934-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,746 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,746 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,746 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/resellers/best-way-to-store-magic-cards-b23904c-25-2585782-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,746 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/resellers/best-way-to-store-magic-cards-b23904c-25-2585782-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,746 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,746 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,746 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/posts/camping-near-celina-ohio-b23904c-25-285636-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,746 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/posts/camping-near-celina-ohio-b23904c-25-285636-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,747 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,747 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,747 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/posts/chloe-snow-boot-b23904c-25-589224-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,747 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/posts/chloe-snow-boot-b23904c-25-589224-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,747 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,747 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,748 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/promo/luxury-shampoo-and-conditioner-b23904c-25-6093532-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,748 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/promo/luxury-shampoo-and-conditioner-b23904c-25-6093532-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,748 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,748 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,748 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/833029/trailer-wheels-14-b23904c-25-911454-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,748 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/833029/trailer-wheels-14-b23904c-25-911454-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,749 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,749 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,749 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/lgbt/timberland-tillston-6-inch-boots-b23904c-25-94254-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,749 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/lgbt/timberland-tillston-6-inch-boots-b23904c-25-94254-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,749 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,749 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,749 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/post/marching-band-drill-paper-b23904c-25-5189048-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,749 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/post/marching-band-drill-paper-b23904c-25-5189048-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,750 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,750 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,750 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/post/army-gun-cleaning-kit-b23904c-25-5245100-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,750 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/post/army-gun-cleaning-kit-b23904c-25-5245100-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,750 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,750 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,750 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/wholesale/circaid-compression-wraps-b23904c-25-4892744-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,750 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/wholesale/circaid-compression-wraps-b23904c-25-4892744-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,751 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,751 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,751 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sitemap.html', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,751 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sitemap.html', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,751 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,751 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,752 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/lgbt/modern-desk-with-hutch-b23904c-25-3571584-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,752 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/lgbt/modern-desk-with-hutch-b23904c-25-3571584-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,752 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,752 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,752 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/vendor/sam-edelman-white-boots-b23904c-25-564810-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,752 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/vendor/sam-edelman-white-boots-b23904c-25-564810-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,753 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,753 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,753 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/discount/how-do-you-fix-hail-damage-on-a-car-b23904c-25-3722112-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,753 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/discount/how-do-you-fix-hail-damage-on-a-car-b23904c-25-3722112-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,753 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,753 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,753 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/suppliers/dedoles-socken-b23904c-25-3365890-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,753 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/suppliers/dedoles-socken-b23904c-25-3365890-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,754 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,754 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,754 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/item/jason-lee-cat-in-the-hat-b23904c-25-2860742-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,754 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/item/jason-lee-cat-in-the-hat-b23904c-25-2860742-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,754 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,754 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,754 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/p/used-hasselblad-cameras-b23904c-25-4623776-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,754 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/p/used-hasselblad-cameras-b23904c-25-4623776-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,755 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,755 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,755 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sale/jerrys-cars-and-parts-b23904c-25-3926554-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,755 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sale/jerrys-cars-and-parts-b23904c-25-3926554-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,756 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,756 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,756 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/common/model-kit-kamen-rider-b23904c-25-5841662-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,756 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/common/model-kit-kamen-rider-b23904c-25-5841662-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,756 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,756 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,756 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://www.instagram.com/aaaexportscom', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,756 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://www.instagram.com/aaaexportscom', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,757 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,757 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,757 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/retailers/mexican-puebla-dress-b23904c-25-2042352-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,757 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/retailers/mexican-puebla-dress-b23904c-25-2042352-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,757 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,757 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,757 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/brands/over-watch-tracker-b23904c-25-3075356-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,757 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/brands/over-watch-tracker-b23904c-25-3075356-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,758 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,758 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,758 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/vendors/best-heated-gloves-for-raynauds-b23904c-25-2766972-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,758 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/vendors/best-heated-gloves-for-raynauds-b23904c-25-2766972-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,758 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,758 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,758 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sellers/uv-phone-sanitizer-with-wireless-charging-pad-b23904c-25-4648766-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,758 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/sellers/uv-phone-sanitizer-with-wireless-charging-pad-b23904c-25-4648766-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,759 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,759 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,759 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/r/haori-cardigan-b23904c-25-1427680-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,759 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/r/haori-cardigan-b23904c-25-1427680-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,759 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,759 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,760 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/gifts/car-accident-lawyer-37419-chattanooga-tn-b23904c-25-3759160-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,760 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/gifts/car-accident-lawyer-37419-chattanooga-tn-b23904c-25-3759160-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,760 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,760 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,760 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/groups/progressive-auto-sales-cars-b23904c-25-3729438-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,760 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/groups/progressive-auto-sales-cars-b23904c-25-3729438-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,761 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,761 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,761 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/retailers/ashley-furniture-outdoor-furniture-b23904c-25-4830210-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,761 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/retailers/ashley-furniture-outdoor-furniture-b23904c-25-4830210-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,761 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,761 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,761 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/wholesale/turbo-pumps-b23904c-25-420410-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,761 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/wholesale/turbo-pumps-b23904c-25-420410-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,762 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,762 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,762 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/members/can-wine-freeze-in-a-car-b23904c-25-3730686-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,762 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/members/can-wine-freeze-in-a-car-b23904c-25-3730686-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,762 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,762 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,762 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/resellers/2005-4runner-lift-kit-b23904c-25-5841010-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,762 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/resellers/2005-4runner-lift-kit-b23904c-25-5841010-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,763 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,763 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (%(scrape_request_ref_id)s, %(website)s, %(url)s, %(depth)s, %(soft_class)s, %(hard_class)s, %(priority_url)s, %(extracted_text)s, %(img_url)s, %(policy)s, %(registered_name)s, %(org_id)s)
2025-07-29 18:03:20,763 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/offers/outdoor-square-table-cover-b23904c-25-5607718-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,763 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'website': 'https://aaaexports.com', 'url': 'https://aaaexports.com/offers/outdoor-square-table-cover-b23904c-25-5607718-2-53', 'depth': 1, 'soft_class': '', 'hard_class': '', 'priority_url': 0, 'extracted_text': '', 'img_url': '', 'policy': '', 'registered_name': '', 'org_id': '4'}
2025-07-29 18:03:20,764 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 18:03:20,764 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 18:03:20,767 [app.utils.website_url_processor] INFO: Successfully stored 46 URLs for scrape_request_ref_id: f0aa52d4-41ca-43a6-8347-0dc710ebf1f1
2025-07-29 18:03:20,768 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 18:03:20,768 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 18:03:20,768 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 18:03:20,768 [sqlalchemy.engine.Engine] INFO: INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(result_status)s, %(mcc_code)s, %(business_category)s, %(business_description)s, %(reasoning)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 18:03:20,768 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'website': 'https://aaaexports.com', 'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T18:03:20.767858Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 18:03:20,768 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'website': 'https://aaaexports.com', 'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'result_status': None, 'mcc_code': None, 'business_category': None, 'business_description': None, 'reasoning': None, 'created_at': '2025-07-29T18:03:20.767858Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 18:03:20,769 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 18:03:20,769 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 18:03:20,771 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 18:03:20,771 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 18:03:20,772 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 18:03:20,772 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-29 18:03:20,772 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'pk_1': 1616}
2025-07-29 18:03:20,772 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'pk_1': 1616}
2025-07-29 18:03:20,773 [app.routers.mcc_analysis] INFO: Created new MCC analysis with ID 1616
2025-07-29 18:03:20,774 [app.routers.mcc_analysis] INFO: Triggered Celery task ab657e1b-635b-4b4c-8591-07baff94cbd6 for analysis 1616
2025-07-29 18:03:20,774 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 18:03:20,774 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:32964 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-29 18:03:20,783 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: f0aa52d4-41ca-43a6-8347-0dc710ebf1f1
2025-07-29 18:03:20,784 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 18:03:20,784 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 18:03:20,784 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:03:20,784 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:03:20,784 INFO sqlalchemy.engine.Engine [cached since 1624s ago] {'scrape_request_ref_id_1': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1'}
2025-07-29 18:03:20,784 [sqlalchemy.engine.Engine] INFO: [cached since 1624s ago] {'scrape_request_ref_id_1': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1'}
2025-07-29 18:03:20,786 [app.utils.website_url_processor] INFO: URLs already exist for scrape_request_ref_id: f0aa52d4-41ca-43a6-8347-0dc710ebf1f1, count: 46
2025-07-29 18:03:20,787 INFO sqlalchemy.engine.Engine INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 18:03:20,787 [sqlalchemy.engine.Engine] INFO: INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (%(website)s, %(scrape_request_ref_id)s, %(analysis_flow_used)s, %(reachability_percentage)s, %(total_urls_processed)s, %(home_page_url)s, %(home_page_text)s, %(home_page_screenshot)s, %(returns_cancellation_exchange_url)s, %(returns_cancellation_exchange_text)s, %(returns_cancellation_exchange_screenshot)s, %(privacy_policy_url)s, %(privacy_policy_text)s, %(privacy_policy_screenshot)s, %(terms_and_condition_url)s, %(terms_and_condition_text)s, %(terms_and_condition_screenshot)s, %(shipping_delivery_url)s, %(shipping_delivery_text)s, %(shipping_delivery_screenshot)s, %(contact_us_url)s, %(contact_us_text)s, %(contact_us_screenshot)s, %(about_us_url)s, %(about_us_text)s, %(about_us_screenshot)s, %(instagram_url)s, %(instagram_text)s, %(instagram_screenshot)s, %(youtube_url)s, %(youtube_text)s, %(youtube_screenshot)s, %(facebook_url)s, %(facebook_text)s, %(facebook_screenshot)s, %(twitter_url)s, %(twitter_text)s, %(twitter_screenshot)s, %(linkedin_url)s, %(linkedin_text)s, %(linkedin_screenshot)s, %(pinterest_url)s, %(pinterest_text)s, %(pinterest_screenshot)s, %(x_url)s, %(x_text)s, %(x_screenshot)s, %(result_status)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(failed_at)s, %(last_updated)s, %(error_message)s, %(details)s, %(org_id)s, %(processing_status)s)
2025-07-29 18:03:20,787 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'website': 'https://aaaexports.com', 'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T18:03:20.786611Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 18:03:20,787 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'website': 'https://aaaexports.com', 'scrape_request_ref_id': 'f0aa52d4-41ca-43a6-8347-0dc710ebf1f1', 'analysis_flow_used': 'normal', 'reachability_percentage': None, 'total_urls_processed': 0, 'home_page_url': None, 'home_page_text': None, 'home_page_screenshot': None, 'returns_cancellation_exchange_url': None, 'returns_cancellation_exchange_text': None, 'returns_cancellation_exchange_screenshot': None, 'privacy_policy_url': None, 'privacy_policy_text': None, 'privacy_policy_screenshot': None, 'terms_and_condition_url': None, 'terms_and_condition_text': None, 'terms_and_condition_screenshot': None, 'shipping_delivery_url': None, 'shipping_delivery_text': None, 'shipping_delivery_screenshot': None, 'contact_us_url': None, 'contact_us_text': None, 'contact_us_screenshot': None, 'about_us_url': None, 'about_us_text': None, 'about_us_screenshot': None, 'instagram_url': None, 'instagram_text': None, 'instagram_screenshot': None, 'youtube_url': None, 'youtube_text': None, 'youtube_screenshot': None, 'facebook_url': None, 'facebook_text': None, 'facebook_screenshot': None, 'twitter_url': None, 'twitter_text': None, 'twitter_screenshot': None, 'linkedin_url': None, 'linkedin_text': None, 'linkedin_screenshot': None, 'pinterest_url': None, 'pinterest_text': None, 'pinterest_screenshot': None, 'x_url': None, 'x_text': None, 'x_screenshot': None, 'result_status': 'PENDING', 'created_at': '2025-07-29T18:03:20.786611Z', 'started_at': None, 'completed_at': None, 'failed_at': None, 'last_updated': None, 'error_message': None, 'details': None, 'org_id': '4', 'processing_status': 'PENDING'}
2025-07-29 18:03:20,788 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 18:03:20,788 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 18:03:20,791 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 18:03:20,791 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 18:03:20,791 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 18:03:20,791 [sqlalchemy.engine.Engine] INFO: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-29 18:03:20,792 INFO sqlalchemy.engine.Engine [cached since 1620s ago] {'pk_1': 1080}
2025-07-29 18:03:20,792 [sqlalchemy.engine.Engine] INFO: [cached since 1620s ago] {'pk_1': 1080}
2025-07-29 18:03:20,793 [app.routers.policy_analysis] INFO: Queuing enhanced policy analysis task for analysis_id: 1080
2025-07-29 18:03:20,794 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 18:03:20,794 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:32964 - "POST /policy-analysis/ HTTP/1.1" 200 OK
2025-07-29 18:29:19,938 [app.routers.mcc_analysis] INFO: Processing MCC analysis request for https://clixsel.com/ with ref_id 01334239-6a8e-4ec0-8f16-0b2dc373e41b
2025-07-29 18:29:19,939 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 18:29:19,939 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 18:29:19,939 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:29:19,939 [sqlalchemy.engine.Engine] INFO: SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:29:19,939 INFO sqlalchemy.engine.Engine [cached since 3183s ago] {'scrape_request_ref_id_1': '01334239-6a8e-4ec0-8f16-0b2dc373e41b'}
2025-07-29 18:29:19,939 [sqlalchemy.engine.Engine] INFO: [cached since 3183s ago] {'scrape_request_ref_id_1': '01334239-6a8e-4ec0-8f16-0b2dc373e41b'}
2025-07-29 18:29:19,945 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:29:19,945 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:29:19,946 INFO sqlalchemy.engine.Engine [cached since 3183s ago] {'scrape_request_ref_id_1': '01334239-6a8e-4ec0-8f16-0b2dc373e41b'}
2025-07-29 18:29:19,946 [sqlalchemy.engine.Engine] INFO: [cached since 3183s ago] {'scrape_request_ref_id_1': '01334239-6a8e-4ec0-8f16-0b2dc373e41b'}
2025-07-29 18:29:19,946 [app.utils.website_url_processor] WARNING: No parsed_urls found in request
2025-07-29 18:29:19,947 [app.routers.mcc_analysis] ERROR: Failed to store URLs
2025-07-29 18:29:19,947 [app.routers.mcc_analysis] ERROR: Error storing URLs: 500: Failed to store website URLs
2025-07-29 18:29:19,947 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 18:29:19,947 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:49804 - "POST /mcc-analysis/ HTTP/1.1" 500 Internal Server Error
2025-07-29 18:29:19,956 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: 01334239-6a8e-4ec0-8f16-0b2dc373e41b
2025-07-29 18:29:19,957 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 18:29:19,957 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 18:29:19,957 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:29:19,957 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-29 18:29:19,957 INFO sqlalchemy.engine.Engine [cached since 3183s ago] {'scrape_request_ref_id_1': '01334239-6a8e-4ec0-8f16-0b2dc373e41b'}
2025-07-29 18:29:19,957 [sqlalchemy.engine.Engine] INFO: [cached since 3183s ago] {'scrape_request_ref_id_1': '01334239-6a8e-4ec0-8f16-0b2dc373e41b'}
2025-07-29 18:29:19,958 [app.utils.website_url_processor] WARNING: No parsed_urls found in request
2025-07-29 18:29:19,959 [app.routers.policy_analysis] ERROR: Error queuing policy analysis: 500: Failed to store website URLs
2025-07-29 18:29:19,959 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 18:29:19,959 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     *************:49804 - "POST /policy-analysis/ HTTP/1.1" 500 Internal Server Error
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     167.94.138.181:57520 - "GET / HTTP/1.1" 404 Not Found
INFO:     167.94.138.181:45920 - "GET / HTTP/1.1" 404 Not Found
INFO:     167.94.138.181:45956 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     167.94.138.181:34074 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     167.94.138.181:56472 - "PRI %2A HTTP/2.0" 404 Not Found
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     167.94.138.181:33700 - "GET /wiki HTTP/1.1" 404 Not Found
WARNING:  Invalid HTTP request received.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-29 20:18:00,590 [app.main] INFO: Application shutting down
INFO:     Application shutdown complete.
INFO:     Finished server process [500266]

import logging
import sys
import json

class ConsoleLogger:
    """
    A simple console logger that outputs structured logs.
    """
    
    def __init__(self, name):
        self.name = name
        self.logger = logging.getLogger(name)
        
        if not self.logger.handlers:
            # Configure logger only if it doesn't have handlers
            self.logger.setLevel(logging.INFO)
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter('%(asctime)s [%(name)s] %(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def _format_extra(self, extra=None):
        """Format the extra data for logging"""
        if extra:
            return f" | {json.dumps(extra)}"
        return ""
    
    def debug(self, message, extra=None):
        """Log debug message with optional extra data"""
        self.logger.debug(f"{message}{self._format_extra(extra)}")
    
    def info(self, message, extra=None):
        """Log info message with optional extra data"""
        self.logger.info(f"{message}{self._format_extra(extra)}")
    
    def warning(self, message, extra=None):
        """Log warning message with optional extra data"""
        self.logger.warning(f"{message}{self._format_extra(extra)}")
    
    def error(self, message, extra=None):
        """Log error message with optional extra data"""
        self.logger.error(f"{message}{self._format_extra(extra)}")
    
    def critical(self, message, extra=None):
        """Log critical message with optional extra data"""
        self.logger.critical(f"{message}{self._format_extra(extra)}") 
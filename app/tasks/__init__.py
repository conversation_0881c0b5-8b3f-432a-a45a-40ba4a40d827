"""
Task package initialization
This ensures all tasks are imported and registered with Celery
"""

# Import tasks to ensure they're registered
from app.tasks.test_task import test_task
from app.tasks.celery_tasks import (
    process_mcc_analysis,
    retry_mcc_analysis,
    health_check_mcc,
    process_url_classification,
    process_url_classification_v2,
    health_check_url_classification,
    retry_url_classification
)
# Import the new unified Policy Analysis Service task
from app.tasks.celery_tasks import process_policy_analysis

# Export all tasks
__all__ = [
    'test_task',
    'process_mcc_analysis',
    'retry_mcc_analysis',
    'health_check_mcc',
    'process_url_classification',
    'process_url_classification_v2',
    'health_check_url_classification',
    'retry_url_classification',
    'process_policy_analysis'
]
# Common desktop browsers - optimized selection
DESKTOP_AGENTS = [
    # Chrome (8 variants - most common platforms and recent versions)
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    # Firefox (8 variants)
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15.5; rv:140.0) Gecko/20100101 Firefox/140.0",
    "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15.4; rv:139.0) Gecko/20100101 Firefox/139.0",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15.3; rv:138.0) Gecko/20100101 Firefox/138.0",
    # Safari (6 variants)
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15",
    # Edge (6 variants)
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.3351.65",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/137.0.3300.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.3351.65",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/136.0.2900.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/137.0.3300.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.2800.0",
    # Opera (6 variants)
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 OPR/124.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/123.0.0.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 OPR/124.0.0.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/123.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/122.0.0.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/123.0.0.0",
    # Brave (6 variants)
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Brave/138.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Brave/*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Brave/138.0.0.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Brave/*********",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Brave/*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Brave/*********",
]

# Mobile browsers - optimized selection
MOBILE_AGENTS = [
    # iOS Safari (8 variants)
    "Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.7 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.7 Mobile/15E148 Safari/604.1",
    # Android Chrome (8 variants)
    "Mozilla/5.0 (Linux; Android 14; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.46 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; SM-A546B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 15; Pixel 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.46 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 14; Pixel 8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.6900.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 14; POCO F6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 12; POCO F4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.6900.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; VOG-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.6900.0 Mobile Safari/537.36",
    # Android Firefox (6 variants)
    "Mozilla/5.0 (Android 16; Mobile; rv:140.0) Gecko/140.0 Firefox/140.0",
    "Mozilla/5.0 (Android 15; Mobile; rv:140.0) Gecko/140.0 Firefox/140.0",
    "Mozilla/5.0 (Android 16; Mobile; rv:139.0) Gecko/139.0 Firefox/139.0",
    "Mozilla/5.0 (Android 14; Mobile; rv:140.0) Gecko/140.0 Firefox/140.0",
    "Mozilla/5.0 (Android 15; Mobile; rv:138.0) Gecko/138.0 Firefox/138.0",
    "Mozilla/5.0 (Android 14; Mobile; rv:139.0) Gecko/139.0 Firefox/139.0",
    # iOS Chrome (6 variants)
    "Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/137.0.7100.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/136.0.6900.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/137.0.7100.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/136.0.6900.0 Mobile/15E148 Safari/604.1",
    # Samsung Browser (6 variants)
    "Mozilla/5.0 (Linux; Android 14; SM-S928B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/28.0 Chrome/130.0.0.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; SM-A546B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/28.0 Chrome/130.0.0.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 15; SM-S938B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/29.0 Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/28.0 Chrome/130.0.0.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; SM-A536B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 12; SM-G988B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/26.0 Chrome/********* Mobile Safari/537.36",
]

# Tablet browsers - optimized selection
TABLET_AGENTS = [
    # iPad (6 variants)
    "Mozilla/5.0 (iPad; CPU OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 17_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 16_7_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.7 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 16_7_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.7 Mobile/15E148 Safari/604.1",
    # Android Tablet (6 variants)
    "Mozilla/5.0 (Linux; Android 15; SM-X910) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.46 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 14; SM-X820) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 15; Lenovo TB-X606F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.46 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; SM-T736B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 14; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.6900.0 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 14; Lenovo TB-J706F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Safari/537.36",
    # Kindle (4 variants)
    "Mozilla/5.0 (Linux; Android 12; KFTRWI) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.46 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; KFMAWI) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 12; KFONWI) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.6900.0 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; KFTRPWI) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Safari/537.36",
    # Huawei Tablets (4 variants)
    "Mozilla/5.0 (Linux; Android 13; HarmonyOS; BAH4-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.46 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; HarmonyOS; AGS3-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 12; HarmonyOS; BAH3-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.6900.0 Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; HarmonyOS; BAH5-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7100.0 Safari/537.36",
]
# Combine all agents
USER_AGENTS = DESKTOP_AGENTS + MOBILE_AGENTS + TABLET_AGENTS

# Dictionary for more targeted selection by device type
USER_AGENTS_BY_TYPE = {
    "desktop": DESKTOP_AGENTS,
    "mobile": MOBILE_AGENTS,
    "tablet": TABLET_AGENTS,
    "all": USER_AGENTS,
}

# Dictionary for more targeted selection by browser
BROWSER_SPECIFIC = {
    "chrome": [
        ua
        for ua in USER_AGENTS
        if "Chrome" in ua and not any(x in ua for x in ["Edg", "OPR", "Brave", "SamsungBrowser"])
    ],
    "firefox": [ua for ua in USER_AGENTS if "Firefox" in ua],
    "safari": [ua for ua in USER_AGENTS if "Safari" in ua and "Chrome" not in ua],
    "edge": [ua for ua in USER_AGENTS if "Edg" in ua],
    "opera": [ua for ua in USER_AGENTS if "OPR" in ua],
    "brave": [ua for ua in USER_AGENTS if "Brave" in ua],
    "samsung": [ua for ua in USER_AGENTS if "SamsungBrowser" in ua],
}

# Dictionary for more targeted selection by OS
OS_SPECIFIC = {
    "windows": [ua for ua in USER_AGENTS if "Windows" in ua],
    "macos": [ua for ua in USER_AGENTS if "Mac OS" in ua],
    "linux": [ua for ua in USER_AGENTS if "Linux" in ua and "Android" not in ua],
    "android": [ua for ua in USER_AGENTS if "Android" in ua],
    "ios": [ua for ua in USER_AGENTS if "iPhone" in ua or "iPad" in ua],
}

# Dictionary for more targeted selection by browser version
BROWSER_VERSION = {
    "chrome_latest": [ua for ua in BROWSER_SPECIFIC["chrome"] if "Chrome/12" in ua],
    "chrome_older": [ua for ua in BROWSER_SPECIFIC["chrome"] if "Chrome/11" in ua or "Chrome/10" in ua],
    "firefox_latest": [ua for ua in BROWSER_SPECIFIC["firefox"] if "Firefox/12" in ua],
    "firefox_older": [ua for ua in BROWSER_SPECIFIC["firefox"] if "Firefox/11" in ua or "Firefox/10" in ua],
}

# Function to get a random user agent based on criteria
def get_random_user_agent(browser_type=None, device_type=None, os_type=None):
    """
    Get a random user agent based on specified criteria.
    
    Args:
        browser_type: Type of browser (chrome, firefox, safari, edge, opera, brave, samsung)
        device_type: Type of device (desktop, mobile, tablet)
        os_type: Type of OS (windows, macos, linux, android, ios)
        
    Returns:
        A random user agent string matching the criteria
    """
    # Start with all user agents
    available_agents = USER_AGENTS
    
    # Filter by browser type if specified
    if browser_type and browser_type in BROWSER_SPECIFIC:
        available_agents = [ua for ua in available_agents if ua in BROWSER_SPECIFIC[browser_type]]
    
    # Filter by device type if specified
    if device_type and device_type in USER_AGENTS_BY_TYPE:
        available_agents = [ua for ua in available_agents if ua in USER_AGENTS_BY_TYPE[device_type]]
    
    # Filter by OS type if specified
    if os_type and os_type in OS_SPECIFIC:
        available_agents = [ua for ua in available_agents if ua in OS_SPECIFIC[os_type]]
    
    # If no agents match the criteria, fall back to all user agents
    if not available_agents:
        available_agents = USER_AGENTS
    
    # Return a random user agent from the filtered list
    import random
    return random.choice(available_agents)

# Function to get a user agent that's less likely to be detected as a bot
def get_stealth_user_agent():
    """
    Get a user agent that's less likely to be detected as a bot.
    Prefers mainstream browsers with recent but not latest versions.
    
    Returns:
        A user agent string optimized for avoiding detection
    """
    import random
    
    # Prefer Chrome or Firefox on Windows or macOS (most common configurations)
    browser_choices = ["chrome", "firefox"]
    os_choices = ["windows", "macos"]
    
    browser = random.choice(browser_choices)
    os_type = random.choice(os_choices)
    
    # Get all matching user agents
    matching_agents = [
        ua for ua in USER_AGENTS 
        if ua in BROWSER_SPECIFIC[browser] and ua in OS_SPECIFIC[os_type]
    ]
    
    # If no matches, fall back to all agents for that browser
    if not matching_agents:
        matching_agents = BROWSER_SPECIFIC[browser]
    
    # Prefer slightly older versions (less likely to trigger bot detection)
    older_versions = [ua for ua in matching_agents if f"{browser.title()}/11" in ua]
    if older_versions:
        return random.choice(older_versions)
    
    return random.choice(matching_agents)

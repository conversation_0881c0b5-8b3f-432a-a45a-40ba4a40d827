"""
Centralized Logger Manager for Production Application
"""
import logging
import sys
import os
from logging.handlers import RotatingFileHandler
from typing import Optional


class LoggerManager:
    """Singleton class for managing application-wide logging configuration."""
    
    _instance: Optional['LoggerManager'] = None
    _loggers: dict = {}
    
    def __new__(cls) -> 'LoggerManager':
        if cls._instance is None:
            cls._instance = super(LoggerManager, cls).__new__(cls)
            cls._instance._initialize_logging()
        return cls._instance
    
    def _initialize_logging(self) -> None:
        """Initialize the logging configuration."""
        # Create logs directory if it doesn't exist
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        
        # Define log format
        self.log_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Add console handler
        self._add_console_handler(root_logger)
        
        # Add file handler
        self._add_file_handler(root_logger, log_dir)
    
    def _add_console_handler(self, logger: logging.Logger) -> None:
        """Add console handler to logger."""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(self.log_format)
        logger.addHandler(console_handler)
    
    def _add_file_handler(self, logger: logging.Logger, log_dir: str) -> None:
        """Add rotating file handler to logger."""
        try:
            file_handler = RotatingFileHandler(
                os.path.join(log_dir, 'app.log'),
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(self.log_format)
            logger.addHandler(file_handler)
        except Exception as e:
            # Fallback to console logging if file handler fails
            console_handler = logging.StreamHandler(sys.stderr)
            console_handler.setLevel(logging.ERROR)
            console_handler.setFormatter(self.log_format)
            logger.addHandler(console_handler)
            logger.error(f"Failed to set up file logging: {e}")
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger instance for the given name."""
        if name not in self._loggers:
            self._loggers[name] = logging.getLogger(name)
        return self._loggers[name]


# Global instance
logger_manager = LoggerManager()

def get_logger(name: str = __name__) -> logging.Logger:
    """Convenience function to get a logger."""
    return logger_manager.get_logger(name)
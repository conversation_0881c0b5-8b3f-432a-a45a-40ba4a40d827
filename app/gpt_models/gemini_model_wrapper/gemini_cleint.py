"""
Placeholder Gemini client that uses OpenAI instead
"""
from typing import Dict, Any, Optional
import json
import os
import time
import random
from app.utils.logger import Console<PERSON>ogger
from openai import OpenAI
from app.config import settings
from google import genai
from google.genai import types
import base64
import os
import dotenv

dotenv.load_dotenv(".env")


class GeminiModelClient:
    """
    A placeholder class for Gemini that uses OpenAI instead.
    This is a temporary solution until Gemini integration is properly set up.
    """
    
    def __init__(self):
        # Use OpenAI client instead
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        
    def get_gemini_response(prompt, website_url="default", task_type="default", model_name="gemini-2.5-flash"):
        # Use OpenAI instead of Gemini
        console_logger = ConsoleLogger("gemini_client")
        console_logger.info("Using OpenAI instead of Gemini", {"task_type": task_type, "website_url": website_url})
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                client = genai.Client(api_key=os.getenv("GEMINI"))
                response = client.models.generate_content(
                model="gemini-2.5-flash",
                contents=prompt,
                config=types.GenerateContentConfig(
                    tools=[types.Tool(
                    # google_search=types.GoogleSearch(),
                    url_context=types.UrlContext()
                    )],
                    automatic_function_calling=types.AutomaticFunctionCallingConfig(maximum_remote_calls=200000),
                    temperature = 0,
                    top_p = 0.95,
                    seed = 0,
                    max_output_tokens = 10000,
                ),
                )
                
                console_logger = ConsoleLogger("gemini_client")
                console_logger.info("Gemini API usage metadata", {"usage": str(response.usage_metadata)})
                time.sleep(10)
                # Wait for the response to be fully generated
                if hasattr(response, 'candidates') and response.candidates:
                    # Make sure the response is complete
                    if hasattr(response.candidates[0], 'finish_reason') and response.candidates[0].finish_reason == 'STOP':
                        if response.text:
                            return response.text
                        else:
                            console_logger = ConsoleLogger("gemini_client")
                            console_logger.warning("Empty response received from Gemini", {"retry": retry_count+1, "max_retries": max_retries})
                            retry_count += 1
                            time.sleep(10)  # Wait before retrying
                    else:
                                                 console_logger = ConsoleLogger("gemini_client")
                         console_logger.warning("Response generation not complete", {"retry": retry_count+1, "max_retries": max_retries})
                        retry_count += 1
                        time.sleep(5)  # Wait longer for incomplete responses
                else:
                                         console_logger = ConsoleLogger("gemini_client")
                     console_logger.warning("No candidates in response", {"retry": retry_count+1, "max_retries": max_retries})
                    retry_count += 1
                    time.sleep(3)  # Wait before retrying
            except Exception as e:
                                 console_logger = ConsoleLogger("gemini_client")
                 console_logger.error("Error in Gemini API call", {"error": str(e), "retry": retry_count+1, "max_retries": max_retries})
                retry_count += 1
                time.sleep(3)  # Wait before retrying
        
        # If we've exhausted all retries
        return "No response from Gemini API after multiple attempts"
        
    def generate_content(self, prompt: str, temperature: float = 0.0) -> Dict[str, Any]:
        """Mock method to generate content using OpenAI instead of Gemini"""
        try:
            response = self.client.chat.completions.create(
                model=settings.OPENAI_DEPLOYMENT_NAME,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": prompt}
                ],
                temperature=temperature,
                max_tokens=10000
            )
            
            text_content = response.choices[0].message.content
            
            # Mimic Gemini response structure
            mock_response = {
                "candidates": [{
                    "content": {
                        "parts": [{
                            "text": text_content
                        }]
                    }
                }],
                "usage_metadata": {
                    "prompt_token_count": response.usage.prompt_tokens,
                    "candidates_token_count": response.usage.completion_tokens,
                    "total_token_count": response.usage.total_tokens
                }
            }
            return mock_response
        except Exception as e:
            console_logger = ConsoleLogger("gemini_client")
            console_logger.error("Error generating content with OpenAI", error=e)
            # Return minimal mock response on error
            return {
                "candidates": [{"content": {"parts": [{"text": "Error generating content"}]}}],
                "usage_metadata": {"prompt_token_count": 0, "candidates_token_count": 0, "total_token_count": 0}
            }
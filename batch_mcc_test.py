import asyncio
import json
import time
import csv
import os
from datetime import datetime
import aiohttp
import logging
import sys
from tabulate import tabulate

# Configure minimal logging - warnings only
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger("batch_mcc_test")

# Configuration and constants
API_KEY = "12345678"
API_1_URL = "https://bffapi.biztel.ai/api/analysis/"
API_2_URL = "https://bffapi.biztel.ai/api/analysis/"
API_MCC_RESULTS = "https://bffapi.biztel.ai/api/mcc/results"
REQUEST_TIMEOUT = 6000 # 10 minutes
BATCH_SIZE = 10  # Match server worker count
DELAY_BETWEEN_BATCHES = 30  # seconds between batch requests
MAX_RETRIES_ON_AUTH_ERROR = 5  # Maximum retries for authorization errors

# Single CSV file for all results
RESULTS_FILE = "mcc_batch_results.csv"

urls = [
    "https://www.supertails.com",
    "https://www.dogspot.in",
    # "https://www.petsworld.in",
    # "https://www.vetina.com",
    # "https://www.pawsindia.com",
    # "https://www.vetplus.co.in",
    # "https://www.wiggles.in",
    # "https://www.royalcanin.in",
    # "https://www.iffco.in",
    # "https://www.nddb.coop",
    # "https://www.kribhco.net",
    # "https://www.kmfnandini.coop",
    # "https://www.verka.coop",
    # "https://www.lalalandscapes.com",
    # "https://www.farmsquare.in",
    # "https://www.greenmylife.in",
    # "https://www.ugaoo.com",
    # "https://www.shapoorjipallonji.com",
    # "https://www.tataprojects.com",
    # "https://www.simplexinfra.com",
    # "https://www.sobha.com",
    # "https://www.brigadegroup.com",
    # "https://www.prestigeconstructions.com",
    # "https://www.voltas.com",
    # "https://www.bluestarindia.com",
    # "https://www.daikinindia.com",
    # "https://www.samsung.com/in/air-conditioners",
    # "https://www.lg.com/in/air-conditioners",
    # "https://www.carrierindia.com",
    # "https://www.haier.com/in/air-conditioners",
    # "https://www.electricalcontractorsindia.com",
    # "https://www.abb.co.in",
    # "https://www.schneider-electric.co.in",
    # "https://www.bheledn.com",
    # "https://www.electricalcontractors.in",
    # "https://www.alstom.com/india",
    # "https://www.siemens.co.in",
    # "https://www.bsesdelhi.com",
    # "https://www.elektracommerce.com",
    # "https://www.stonesourceindia.com",
    # "https://www.apstile.com",
    # "https://www.asiaceramic.com",
    # "https://www.orientbell.com",
    # "https://www.hrjohnsonindia.com",
    # "https://www.simola.in",
    # "https://www.kajariaceramics.com",
    # "https://www.rajtile.com",
    # "https://www.somanyceramics.com",
    # "https://www.parasceramic.com",
    # "https://www.woodtechcontractors.com",
    # "https://www.carpenterhub.in",
    # "https://www.indiancontractsolutions.com",
    # "https://www.furnotechsolutions.com",
    # "https://www.carpentrycontractors.com",
    # "https://www.raasdesigns.com",
    # "https://www.homecraftcontractors.in",
    # "https://www.houzz.in/pro/carpenters",
    # "https://www.urbancompany.com/carpenters",
    # "https://www.handymancontractors.com",
    # "https://www.jswsteel.com",
    # "https://www.bansalroofings.com",
    # "https://www.roofindia.com",
    # "https://www.tataroofing.com",
    # "https://www.everestind.com",
    # "https://www.ashokaispat.in",
    # "https://www.roofindiaexpo.com",
    # "https://www.vrroofings.com",
    # "https://www.hitechroofings.com",
    # "https://www.polyroof.com",
    # "https://www.icjonline.com",
    # "https://www.paschalindia.com",
    # "https://www.icikochi.org",
    # "https://www.ultratechcement.com",
    # "https://www.lntecc.com",
    # "https://www.prismjohnson.in",
    # "https://www.ramcocements.in",
    # "https://www.acc.com",
    # "https://www.dalmiasugar.com",
    # "https://www.jswcement.in",
    # "https://www.leaassociates.com",
    # "https://www.rjcontractors.in",
    # "https://www.shapoorji.in",
    # "https://www.obayashi-india.com",
    # "https://www.gammonindia.com",
    # "https://www.larsentoubro.com",
    # "https://www.ahluwalia.co.in",
    # "https://www.relianceinfra.com",
    # "https://www.gannon-dunkerley.com",
    # "https://www.macmillaneducation.in",
    # "https://www.scholastic.co.in",
    # "https://www.sapnaonline.com",
    # "https://www.reproindialtd.com",
    # "https://www.pearson.com",
    # "https://www.oxford.co.in",
    # "https://www.sagepub.in",
    # "https://www.vikaspublishing.com",
    # "https://www.kalpagampublications.com",
    # "https://www.tatamcgrawhill.com",
    # "https://www.print2india.com",
    # "https://www.adityaprinters.com",
    # "https://www.printbazaar.in",
    # "https://www.vishwagroup.co.in",
    # "https://www.paragonprinters.com",
    # "https://www.jayantiprinterspvtltd.com",
    # "https://www.akprinterspvtltd.com",
    # "https://www.shahprinterspune.com",
    # "https://www.sahilprinting.com",
    # "https://www.printindia.co.in",
    # "https://www.reliancepolymers.com",
    # "https://www.eureka-forbes.com",
    # "https://www.hindustanunilever.com",
    # "https://www.jayemauto.com",
    # "https://www.divyafabrics.com",
    # "https://www.borosilcleaning.com",
    # "https://www.colgatepalmolive.co.in",
    # "https://www.3mindia.in",
    # "https://www.kanpurgroup.com",
    # "https://www.ipceagleindia.com",
    # "https://www.indianrailways.gov.in",
    # "https://www.irctc.co.in",
    # "https://www.dfccil.com",
    # "https://www.indianrailwayscatering.com",
    # "https://www.concorindia.co.in",
    # "https://www.rldc.co.in",
    # "https://www.cris.org.in",
    # "https://www.rites.com",
    # "https://www.metrorailgurgaon.com",
    # "https://www.rcf.indianrailways.gov.in",
    # "https://www.bestundertaking.com",
    # "https://www.mumbaimetro.one",
    # "https://www.mmrcl.com",
    # "https://www.msrtc.maharashtra.gov.in",
    # "https://www.konkanrailway.com",
    # "https://www.mumbaibuses.com",
    # "https://www.metrorailnagpur.com",
    # "https://www.cr.indianrailways.gov.in",
    # "https://www.delhimetrorail.com",
    # "https://www.metrorailmumbai.com",
    # "https://www.ner.indianrailways.gov.in",
    # "https://www.swr.indianrailways.gov.in",
    # "https://www.zhl.org.in",
    # "https://www.emsindia.in",
    # "https://www.108ambulance.com",
    # "https://www.lifelineambulance.in",
    # "https://www.apollohospitals.com",
    # "https://www.medilifeline.in",
    # "https://www.nationalambulance.org",
    # "https://www.medicaltransport.com",
    # "https://www.caremed.in",
    # "https://www.transcareambulance.com",
    # "https://www.olacabs.com",
    # "https://www.uber.com/in/en",
    # "https://www.merucabs.com",
    # "https://www.easemytrip.com/cabs",
    # "https://www.karyacabs.com",
    # "https://www.savaaricars.com",
    # "https://www.bookcab.in",
    # "https://www.gozo.in",
    # "https://www.blacklane.com",
    # "https://www.quickride.in",
    # "https://www.redbus.in",
    # "https://www.makemytrip.com/bus-tickets.html",
    # "https://www.abhibus.com",
    # "https://www.ksrtc.in",
    # "https://www.srsbooking.com",
    # "https://www.paibusticket.com",
    # "https://www.vrlbus.in",
    # "https://www.tnstc.in",
    # "https://www.goibibo.com/bus-tickets",
    # "https://www.parveenexpress.com",
    # "https://www.gati.com",
    # "https://www.agarwalpackers.com",
    # "https://www.tci.in",
    # "https://www.safexpress.com",
    # "https://www.blue-dart.com",
    # "https://www.dhl.co.in",
    # "https://www.transindia.com",
    # "https://www.globeecargo.com",
    # "https://www.vrlgroup.in",
    # "https://www.allcargo.com",
    # "https://www.dtdc.com",
    # "https://www.fedex.com/en-in/home.html",
    # "https://www.delhivery.com",
    # "https://www.bluedart.com",
    # "https://www.xpressbees.com",
    # "https://www.shree-maruti.com",
    # "https://www.shadowfax.in",
    # "https://www.aramex.com",
    # "https://www.garudavega.com",
    # "https://www.novelfreight.com",
    # "https://www.tcil.com",
    # "https://www.drs-group.in",
    # "https://www.bharatpackersmovers.com",
    # "https://www.expressworld.com",
    # "https://www.smllogistics.com",
    # "https://www.cochinport.gov.in",
    # "https://www.vizagport.com",
    # "https://www.adaniports.com",
    # "https://www.shippingindia.com",
    # "https://www.hindship.com"
]


EXPECTED_MCC= {url: None for url in urls}

class WebsiteResult:
    def __init__(self, website, request_time=None):
        self.website = website
        self.request_time = request_time
        self.ref_id = None
        self.mcc = None
        self.status = "PENDING"
        self.time_taken = None
        self.expected_mcc = EXPECTED_MCC.get(website)
    
    def to_row(self):
        return [
            self.website,
            self.mcc if self.mcc is not None else "None",
            self.status,
            f"{self.time_taken:.2f}s" if self.time_taken is not None else "N/A",
            self.ref_id if self.ref_id is not None else "None"
        ]
    
    def to_dict(self):
        return {
            "website": self.website,
            "mcc": self.mcc,
            "status": self.status,
            "timetaken": self.time_taken if self.time_taken is not None else "N/A",
            "refid": self.ref_id
        }

async def request_analysis(session, website):
    """Send a request to the API to analyze a website"""
    payload = {"website": website, "registeredEntityName": ""}
    headers = {"X-API-KEY": API_KEY, "Content-Type": "application/json"}

    result = WebsiteResult(website, datetime.now())

    # Auth error retry loop
    for retry in range(MAX_RETRIES_ON_AUTH_ERROR + 1):
        try:
            async with session.post(API_1_URL, json=payload, headers=headers, timeout=REQUEST_TIMEOUT) as response:
                # Handle specific HTTP error codes
                if response.status == 401:
                    if retry < MAX_RETRIES_ON_AUTH_ERROR:
                        await asyncio.sleep(2)
                        continue
                    else:
                        result.status = "AUTH_ERROR"
                        await update_csv_realtime(result)
                        return result
                elif response.status == 429:
                    result.status = "RATE_LIMITED"
                    await update_csv_realtime(result)
                    return result
                elif response.status != 200:
                    result.status = f"REQUEST_FAILED_{response.status}"
                    await update_csv_realtime(result)
                    return result

                try:
                    response_data = await response.json()
                    if response_data.get("success"):
                        result.ref_id = response_data.get("refId")
                        # Update CSV immediately with pending status
                        await update_csv_realtime(result)
                        return result
                    else:
                        error_msg = response_data.get("message", "Unknown error")
                        result.status = f"API_ERROR_{error_msg[:20]}"
                        await update_csv_realtime(result)
                        return result
                except json.JSONDecodeError:
                    result.status = "INVALID_JSON"
                    await update_csv_realtime(result)
                    return result
        except Exception as e:
            result.status = f"ERROR_{str(e)[:30]}"
            await update_csv_realtime(result)
            return result

    result.status = "MAX_RETRIES_EXCEEDED"
    await update_csv_realtime(result)
    return result

async def update_csv_realtime(result):
    """Update CSV file in real-time with current result"""
    try:
        # Check if file exists and has header
        file_exists = os.path.exists(RESULTS_FILE)

        # Read existing data to check if this website already exists
        existing_data = {}
        if file_exists:
            try:
                with open(RESULTS_FILE, 'r', newline='') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        existing_data[row['website']] = row
            except Exception:
                pass

        # Update or add the current result
        existing_data[result.website] = {
            'website': result.website,
            'mcc': result.mcc if result.mcc is not None else "None",
            'status': result.status,
            'timetaken': f"{result.time_taken:.2f}" if result.time_taken is not None else "N/A",
            'refid': result.ref_id if result.ref_id is not None else "None"
        }

        # Write all data back to file
        with open(RESULTS_FILE, 'w', newline='') as f:
            fieldnames = ['website', 'mcc', 'status', 'timetaken', 'refid']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for data in existing_data.values():
                writer.writerow(data)

    except Exception as e:
        logger.warning(f"Failed to update CSV for {result.website}: {str(e)}")

async def get_analysis_result(session, ref_id):
    """Get the analysis result for a given ref_id"""
    headers = {"X-API-KEY": API_KEY}
    
    # Try both endpoints regardless of success to be thorough
    primary_response = None
    mcc_response = None
    
    # Try the primary endpoint
    try:
        async with session.get(f"{API_2_URL}{ref_id}", headers=headers, timeout=REQUEST_TIMEOUT) as response:
            if response.status == 200:
                try:
                    primary_response = await response.json()
                except json.JSONDecodeError:
                    pass
            elif response.status == 401:
                # Auth error, don't retry here as this isn't the initial request
                return {"success": False, "status": "AUTH_ERROR"}
    except Exception:
        pass
    
    # Always try the MCC endpoint as well, regardless of primary success
    try:
        async with session.get(f"{API_MCC_RESULTS}/{ref_id}", headers=headers, timeout=REQUEST_TIMEOUT) as response:
            if response.status == 200:
                try:
                    mcc_data = await response.json()
                    # Format consistent with primary endpoint
                    mcc_response = {
                        "success": True,
                        "status": mcc_data.get("status", "UNKNOWN"),
                        "mccResult": {
                            "mcc": mcc_data.get("mcc", None)
                        }
                    }
                except json.JSONDecodeError:
                    pass
            elif response.status == 401:
                if not primary_response:  # Only return auth error if primary didn't succeed
                    return {"success": False, "status": "AUTH_ERROR"}
    except Exception:
        pass
    
    # Now determine which result to return
    # Prefer completed results from either endpoint
    if primary_response and primary_response.get('status') == 'COMPLETED':
        return primary_response
    elif mcc_response and mcc_response.get('status') == 'COMPLETED':
        return mcc_response
    # If we have a primary response with any status, use that
    elif primary_response:
        return primary_response
    # Otherwise use MCC response if available
    elif mcc_response:
        return mcc_response
    # If nothing worked, return None
    return None

async def check_results(session, results, result_queue):
    """Check results for all pending websites"""
    pending_results = [r for r in results if r.status == "PENDING" and r.ref_id is not None]

    # Log the number of pending results we're checking
    print(f"\rChecking {len(pending_results)} pending results...", end="", flush=True)

    for result in pending_results:
        try:
            response = await get_analysis_result(session, result.ref_id)

            if response and isinstance(response, dict):
                # Calculate time from initial request
                current_time = datetime.now()
                elapsed = (current_time - result.request_time).total_seconds()

                # Handle auth errors
                if response.get('status') == 'AUTH_ERROR':
                    result.status = "AUTH_ERROR"
                    result.time_taken = elapsed
                    await update_csv_realtime(result)
                    await result_queue.put(result)
                    continue

                # Check for COMPLETED status regardless of success flag
                if response.get('status') == 'COMPLETED':
                    result.status = "COMPLETED"
                    result.time_taken = elapsed
                    mcc_result = response.get('mccResult', {})
                    result.mcc = mcc_result.get('mcc')
                    await update_csv_realtime(result)
                    await result_queue.put(result)
                    continue

                # Handle other statuses when success is True
                if response.get('success'):
                    status = response.get('status')

                    # Only update status if it's definitive (not still processing)
                    if status == 'FAILED' or status == 'ERROR':
                        result.status = status
                        result.time_taken = elapsed
                        # Get any error message
                        if 'message' in response:
                            result.status = f"{status}_{response['message'][:20]}"
                        await update_csv_realtime(result)
                        await result_queue.put(result)
                        continue
                    # For other statuses (e.g. PROCESSING, PENDING) keep waiting

                # Check error messages for clues
                if not response.get('success') and 'message' in response:
                    # If it's a non-recoverable error, mark it and continue
                    message = response.get('message', '')
                    if 'not found' in message.lower() or 'invalid' in message.lower():
                        result.status = f"ERROR_{message[:20]}"
                        result.time_taken = elapsed
                        await update_csv_realtime(result)
                        await result_queue.put(result)
                        continue
        except Exception as e:
            # On exception, just continue checking other websites
            logger.warning(f"Exception checking result for {result.website}: {str(e)}")
            pass

async def display_progress(total, result_queue):
    """Display progress - CSV updates are handled in real-time by update_csv_realtime"""
    completed = 0

    while completed < total:
        result = await result_queue.get()
        completed += 1

        # Print progress with more details
        status_color = "✓" if result.status == "COMPLETED" else "✗" if result.status.startswith("ERROR") else "⏳"
        print(f"\r{status_color} Progress: {completed}/{total} ({completed/total*100:.1f}%) - Latest: {result.website[:50]}{'...' if len(result.website) > 50 else ''}", end="", flush=True)

    print("\nAll results collected!")

async def send_batch_requests(session, websites, result_queue):
    """Send a batch of website requests in parallel"""
    all_results = []

    for i in range(0, len(websites), BATCH_SIZE):
        batch = websites[i:i+BATCH_SIZE]
        batch_start_time = time.time()

        # Process each batch in parallel
        batch_tasks = [request_analysis(session, website) for website in batch]
        batch_results = await asyncio.gather(*batch_tasks)

        for result in batch_results:
            all_results.append(result)

            # All results are now updated in CSV in real-time, so put all in queue
            if result.status != "PENDING":
                await result_queue.put(result)

        # Print batch info
        batch_time = time.time() - batch_start_time
        print(f"\rSent batch {i//BATCH_SIZE + 1}/{(len(websites) + BATCH_SIZE - 1)//BATCH_SIZE} " +
              f"({i+1}-{min(i+BATCH_SIZE, len(websites))}/{len(websites)}) in {batch_time:.2f}s",
              end="", flush=True)

        # Delay between batches (unless it's the last batch)
        if i + BATCH_SIZE < len(websites):
            await asyncio.sleep(DELAY_BETWEEN_BATCHES)

    print("\nAll website requests sent!")
    return all_results

async def main():
    # Initialize the single CSV file
    if os.path.exists(RESULTS_FILE):
        print(f"Found existing results file: {RESULTS_FILE}")
        print("Results will be updated in real-time. Past records will be maintained.")
    else:
        print(f"Creating new results file: {RESULTS_FILE}")
        # Create empty CSV with headers
        with open(RESULTS_FILE, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['website', 'mcc', 'status', 'timetaken', 'refid'])

    # Get list of websites
    websites = list(EXPECTED_MCC.keys())
    total_websites = len(websites)

    # Calculate a reasonable total timeout
    # If server processes 4 at a time and each takes ~15 minutes worst case,
    # total time would be around (total_websites / 4) * 15 minutes + buffer
    estimated_server_process_time = (total_websites / 4) * 900  # 15 minutes per website
    # Add buffer for network delays, retries, etc.
    TOTAL_TIMEOUT = max(estimated_server_process_time * 1.5, 3600)  # at least 1 hour

    print(f"Starting batch MCC test for {total_websites} websites")
    print(f"Batch size: {BATCH_SIZE} websites every {DELAY_BETWEEN_BATCHES} seconds")
    print(f"Estimated total runtime: {TOTAL_TIMEOUT/60:.1f} minutes (based on server processing 4 sites at a time)")
    print(f"Results will be updated in real-time to: {RESULTS_FILE}")

    # Create a queue for completed results
    result_queue = asyncio.Queue()

    # Create TCP connector with appropriate settings
    tcp_connector = aiohttp.TCPConnector(
        keepalive_timeout=60,
        limit=100,
        ttl_dns_cache=300
    )

    session_timeout = aiohttp.ClientTimeout(total=None, sock_connect=60, sock_read=60)

    # Start progress display task
    progress_task = asyncio.create_task(display_progress(total_websites, result_queue))
    
    async with aiohttp.ClientSession(connector=tcp_connector, timeout=session_timeout) as session:
        # First phase: Send all requests in batches
        batch_start_time = time.time()
        print("Phase 1: Sending requests to all websites in batches...")
        
        all_results = await send_batch_requests(session, websites, result_queue)
        
        batch_time = time.time() - batch_start_time
        print(f"All {total_websites} websites requested in {batch_time:.2f} seconds")
        
        # Second phase: Poll for results
        print("Phase 2: Collecting results...")
        print(f"The server processes 4 websites at a time, so results will come in batches")
        
        # Keep checking until all results are complete or timeout
        check_interval = 60  # Check every 60 seconds
        
        # Start the timer for the overall process
        start_time = time.time()
        last_status_time = start_time
        
        while any(r.status == "PENDING" for r in all_results):
            await check_results(session, all_results, result_queue)
            
            # Print status every 2 minutes
            current_time = time.time()
            if current_time - last_status_time > 120:
                pending_count = sum(1 for r in all_results if r.status == "PENDING")
                completed_count = total_websites - pending_count
                elapsed_minutes = (current_time - start_time) / 60
                print(f"\nStatus after {elapsed_minutes:.1f} minutes: {completed_count}/{total_websites} completed, {pending_count} pending")
                last_status_time = current_time
            
            # Check if we've exceeded our total time budget
            if time.time() - start_time > TOTAL_TIMEOUT:
                pending_count = sum(1 for r in all_results if r.status == "PENDING")
                print(f"\nTotal timeout of {TOTAL_TIMEOUT/60:.1f} minutes exceeded. Still have {pending_count} pending results.")
                break
                
            # Only sleep if we still have pending results
            if any(r.status == "PENDING" for r in all_results):
                await asyncio.sleep(check_interval)
        
        # Mark any remaining pending results as timed out
        for result in all_results:
            if result.status == "PENDING":
                result.status = "TIMEOUT_EXCEEDED"
                result.time_taken = (datetime.now() - result.request_time).total_seconds()
                await update_csv_realtime(result)
                await result_queue.put(result)

    # Wait for progress display to complete
    await progress_task

    # Calculate statistics
    completed_count = sum(1 for r in all_results if r.status == "COMPLETED")
    failed_count = sum(1 for r in all_results if r.status != "COMPLETED")

    print("\nTest Results Summary:")
    print(f"Total websites: {total_websites}")
    print(f"Completed: {completed_count}")
    print(f"Failed: {failed_count}")

    # Print results table
    table_data = [r.to_row() for r in all_results]
    print("\nResults Table:")
    print(tabulate(table_data[:10], headers=['website', 'mcc', 'status', 'timetaken', 'refid'], tablefmt='grid'))
    print("... (showing first 10 rows, all results in CSV file)")

    print(f"\nAll results have been updated in real-time to: {RESULTS_FILE}")
    print("The CSV file contains the most up-to-date results with past records maintained.")

if __name__ == "__main__":
    asyncio.run(main()) 

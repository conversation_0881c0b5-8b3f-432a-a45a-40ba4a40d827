#!/usr/bin/env python3
"""
MySQL Database Initialization Script
- Tests connection to MySQL database
- Creates all tables defined in app.models.db_models
"""

import sys
import os
from sqlalchemy import text, inspect
from sqlmodel import SQLModel

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.models.db_models import engine
# Import all models to ensure they are registered with SQLModel
from app.models import db_models

def test_mysql_connection():
    """Test connectivity to MySQL database"""
    print("🔌 Testing MySQL database connection...")
    try:
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            test_value = result.fetchone()[0]
            if test_value == 1:
                print("✅ MySQL connection successful!")
                db_info = connection.execute(text("SELECT DATABASE(), VERSION()")).fetchone()
                print(f"   Database: {db_info[0]}")
                print(f"   MySQL Version: {db_info[1]}")
                return True
            else:
                print("❌ MySQL connection test failed")
                return False
    except Exception as e:
        print(f"❌ MySQL connection failed: {str(e)}")
        return False

def create_all_tables():
    """Create all tables defined in db_models"""
    print("\n🏗️  Creating all defined tables...")
    try:
        # SQLModel.metadata.create_all() will create all tables that inherit from SQLModel
        # and have been imported.
        SQLModel.metadata.create_all(engine)
        
        print("   Verifying table creation...")
        inspector = inspect(engine)
        all_tables = inspector.get_table_names()
        
        print(f"   Found {len(all_tables)} tables in the database:")
        for table in sorted(all_tables):
            print(f"     - {table}")
            
        print("\n   ✅ Table creation process completed.")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {str(e)}")
        import traceback
        print("Full traceback:")
        print(traceback.format_exc())
        return False

def main():
    """Main function to run the database initialization"""
    print("="*60)
    print("🚀 Starting Database Initialization")
    print("="*60)

    if not test_mysql_connection():
        sys.exit(1)
        
    if not create_all_tables():
        sys.exit(1)

    print("\n🎉 Database initialization finished successfully!")

if __name__ == "__main__":
    main() 
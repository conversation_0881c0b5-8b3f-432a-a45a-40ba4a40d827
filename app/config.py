import os

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

load_dotenv(".env")


class Settings(BaseSettings):
    DATABASE_URL: str = os.getenv("DATABASE_URL")
    APP_NAME: str = "FastAPI Service"
    DEBUG: bool = True
    AZURE_API_TYPE: str = os.getenv("azure_api_type")
    AZURE_API_BASE: str = os.getenv("azure_api_base")
    AZURE_API_VERSION: str = os.getenv("azure_api_version")
    AZURE_API_KEY: str = os.getenv("azure_api_key")
    AZURE_DEPLOYMENT_NAME: str = os.getenv("azure_deployment_name")
    OPENAI_API_KEY: str = os.getenv("openai_api_key")
    OPENAI_DEPLOYMENT_NAME: str = os.getenv("openai_deployment_name")
    BASE_URL: str = os.getenv("base_url")
    ADMIN_USERNAME: str = os.getenv("admin_username")
    ADMIN_PASSWORD: str = os.getenv("admin_password")
    INPUT_PATH: str = os.getenv("input_path")
    OUTPUT_PATH: str = os.getenv("output_path")
    BIZTEL_API_KEY: str = os.getenv("biztel_api_key")
    CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND")
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY")
    WEBSHARE_PROXY_USERNAME: str = os.getenv("WEBSHARE_PROXY_USERNAME", "")
    WEBSHARE_PROXY_PASSWORD: str = os.getenv("WEBSHARE_PROXY_PASSWORD", "")
    class Config:
        env_file = ".env"
        extra = "ignore"  # Ignore extra fields instead of raising validation errors


settings = Settings()

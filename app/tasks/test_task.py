"""
Test task for verifying Celery configuration
"""
import time
from app.celery_app import celery_app

@celery_app.task(name="test_task")
def test_task(message: str = "Hello Celery"):
    """
    A simple test task to verify Celery configuration
    
    Returns:
        dict: Test result containing input message and timestamp
    """
    time.sleep(2)  # Simulate some work
    
    return {
        "status": "success",
        "message": message,
        "timestamp": time.time()
    } 
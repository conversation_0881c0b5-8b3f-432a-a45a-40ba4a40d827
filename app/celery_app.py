"""
Central module for Celery app instance to avoid circular imports
"""
from celery import Celery
from app.config import settings

# Initialize Celery
celery_app = Celery(
    "analysis_tasks",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=1800,  # 30 minutes timeout
    worker_max_tasks_per_child=200,  # Restart worker after 200 tasks
    broker_connection_retry_on_startup=True,
    include=['app.tasks.test_task', 'app.tasks.celery_tasks'],

    # Dedicated queue routing for optimal performance on 4 vCPU instance
    task_routes={
        # MCC Analysis - CPU intensive, dedicated queue
        'process_mcc_analysis': {'queue': 'mcc_queue'},
        'retry_mcc_analysis': {'queue': 'mcc_queue'},
        'health_check_mcc': {'queue': 'mcc_queue'},

        # Policy Analysis - I/O + CPU intensive, dedicated queue
        'process_policy_analysis': {'queue': 'policy_queue'},
        'process_policy_analysis_enhanced': {'queue': 'policy_queue'},

        # Risky Classification - I/O + CPU intensive, dedicated queue
        'process_risky_classification': {'queue': 'risky_queue'},

        # General tasks - URL classification, health checks, etc.
        'process_url_classification': {'queue': 'general_queue'},
        'process_url_classification_v2': {'queue': 'general_queue'},
        'retry_url_classification': {'queue': 'general_queue'},
        'health_check_url_classification': {'queue': 'general_queue'},
        'test_task': {'queue': 'general_queue'},
    },

    # Worker configuration optimized for 4 vCPU instance
    worker_prefetch_multiplier=1,  # Prevent worker hoarding tasks
    task_acks_late=True,  # Acknowledge tasks after completion
    worker_disable_rate_limits=False,  # Keep rate limits for API protection
)

# Include task modules
celery_app.autodiscover_tasks(['app.tasks'], force=True)

# Import tasks to ensure they're registered
import app.tasks 
"""
Celery configuration and task registration
"""
from app.celery_app import celery_app

# Register tasks with specific names
@celery_app.task(
    name='app.tasks.mcc_task.execute_mcc_task',
    bind=True,
    time_limit= 180,  # 3 minutes hard timeout
    soft_time_limit=120,  # 2 minutes soft timeout (sends exception before hard timeout)
    max_retries=3,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=60,  # Maximum backoff time (1 minute)
    retry_jitter=True,  # Add random jitter to retry delay
    rate_limit='4/m',  # Rate limit: 4 tasks per minute
    autoretry_for=(Exception,),  # Auto retry on exceptions
    acks_late=True,  # Task acknowledgment after execution (ensures task is re-queued if worker crashes)
    track_started=True  # Track when task is started
)
def execute_mcc_task_wrapper(self, website_url, scrape_request_ref_id, org_id):
    """
    DEPRECATED: Use app.tasks.mcc_task.execute_mcc_task_new instead
    
    This task is maintained for backward compatibility with existing code.
    
    Args:
        self: Task instance (added with bind=True)
        website_url: URL of the website to analyze
        scrape_request_ref_id: Reference ID for the scrape request
        org_id: Organization ID
        
    Returns:
        Results from the MCC analysis task
    """
    # We'll import this function only when it's called to avoid circular imports
    try:
        from app.tasks.celery_tasks import execute_mcc_task
        return execute_mcc_task(website_url, scrape_request_ref_id, org_id)
    except Exception as exc:
        self.retry(exc=exc, countdown=60 * (2 ** (self.request.retries)))  # Exponential backoff 

@celery_app.task(
    name='app.tasks.mcc_task.execute_mcc_task_new',
    bind=True,
    time_limit= 180,  # 3 minutes hard timeout
    soft_time_limit=120,  # 2 minutes soft timeout (sends exception before hard timeout)
    max_retries=3,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=60,  # Maximum backoff time (1 minute)
    retry_jitter=True,  # Add random jitter to retry delay
    rate_limit='4/m',  # Rate limit: 4 tasks per minute
    autoretry_for=(Exception,),  # Auto retry on exceptions
    acks_late=True,  # Task acknowledgment after execution (ensures task is re-queued if worker crashes)
    track_started=True  # Track when task is started
)
def execute_mcc_task_new_wrapper(self, website_url, scrape_request_ref_id, org_id, **kwargs):
    """
    Wrapper function for the modular MCC analysis task
    
    Args:
        self: Task instance (added with bind=True)
        website_url: URL of the website to analyze
        scrape_request_ref_id: Reference ID for the scrape request
        org_id: Organization ID
        **kwargs: Additional parameters
        
    Returns:
        Results from the MCC analysis task
    """
    try:
        from app.tasks.celery_tasks import execute_mcc_task_new
        return execute_mcc_task_new(website_url, scrape_request_ref_id, org_id, **kwargs)
    except Exception as exc:
        self.retry(exc=exc, countdown=60 * (2 ** (self.request.retries)))  # Exponential backoff

@celery_app.task(
    name='app.tasks.risky.analyze_website_task',
    bind=True,
    time_limit=600,  # 10 minutes hard timeout
    soft_time_limit=540,  # 9 minutes soft timeout
    max_retries=2,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=300,  # Maximum backoff time (5 minutes)
    retry_jitter=True,  # Add random jitter to retry delay
    rate_limit='2/m',  # Rate limit: 2 tasks per minute (due to API usage)
    autoretry_for=(Exception,),  # Auto retry on exceptions
    acks_late=True,  # Task acknowledgment after execution
    track_started=True  # Track when task is started
)
def analyze_risky_website_task(self, website_url, expected_risk="", org_id="default", provided_urls=None):
    """
    DEPRECATED: Use app.tasks.risky_task.execute_risky_task_new instead
    
    This task is maintained for backward compatibility with existing code.
    """
    try:
        from app.tasks.celery_tasks import analyze_website
        analyze_website(website_url, expected_risk, org_id, provided_urls)
        return {"status": "completed", "website": website_url}
    except Exception as exc:
        self.retry(exc=exc, countdown=120 * (2 ** (self.request.retries)))  # Exponential backoff 

@celery_app.task(
    name='app.tasks.risky_task.execute_risky_task_new',
    bind=True,
    time_limit=600,  # 10 minutes hard timeout
    soft_time_limit=540,  # 9 minutes soft timeout
    max_retries=2,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=300,  # Maximum backoff time (5 minutes)
    retry_jitter=True,  # Add random jitter to retry delay
    rate_limit='2/m',  # Rate limit: 2 tasks per minute (due to API usage)
    autoretry_for=(Exception,),  # Auto retry on exceptions
    acks_late=True,  # Task acknowledgment after execution
    track_started=True  # Track when task is started
)
def execute_risky_task_wrapper(self, website_url, scrape_request_ref_id, org_id, **kwargs):
    """
    Wrapper function for the modular Risky analysis task
    
    Args:
        self: Task instance (added with bind=True)
        website_url: URL of the website to analyze
        scrape_request_ref_id: Reference ID for the scrape request
        org_id: Organization ID
        **kwargs: Additional parameters like provided_urls
        
    Returns:
        Results from the Risky analysis task
    """
    try:
        from app.tasks.celery_tasks import execute_risky_task_new
        return execute_risky_task_new(website_url, scrape_request_ref_id, org_id, **kwargs)
    except Exception as exc:
        self.retry(exc=exc, countdown=120 * (2 ** (self.request.retries)))  # Exponential backoff

@celery_app.task(
    name='app.tasks.social_media.execute_social_media_task',
    bind=True,
    time_limit=900,  # 15 minutes hard timeout
    soft_time_limit=840,  # 14 minutes soft timeout
    max_retries=2,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=300,  # Maximum backoff time (5 minutes)
    retry_jitter=True,  # Add random jitter to retry delay
    rate_limit='3/m',  # Rate limit: 3 tasks per minute (due to API usage)
    autoretry_for=(Exception,),  # Auto retry on exceptions
    acks_late=True,  # Task acknowledgment after execution
    track_started=True  # Track when task is started
)
def execute_social_media_task_wrapper(self, website_url, scrape_request_ref_id, org_id, **kwargs):
    """
    Wrapper function for the Social Media analysis task
    
    Args:
        self: Task instance (added with bind=True)
        website_url: URL of the website to analyze
        scrape_request_ref_id: Reference ID for the scrape request
        org_id: Organization ID
        **kwargs: Additional parameters like social_media_links
        
    Returns:
        Results from the Social Media analysis task
    """
    try:
        from app.tasks.celery_tasks import execute_social_media_task
        return execute_social_media_task(website_url, scrape_request_ref_id, org_id, **kwargs)
    except Exception as exc:
        self.retry(exc=exc, countdown=120 * (2 ** (self.request.retries)))  # Exponential backoff

@celery_app.task(
    name='app.tasks.policy_task.policy_analysis_task',
    bind=True,
    time_limit=2700,  # 45 minutes hard timeout (increased from 30)
    soft_time_limit=2400,  # 40 minutes soft timeout (increased from 20)
    max_retries=3,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=900,  # Maximum backoff time (15 minutes, increased from 10)
    retry_jitter=True,  # Add random jitter to retry delay
)
def policy_analysis_task_wrapper(self, website_url, scrape_request_ref_id, parsed_urls_data, org_id):
    """
    Wrapper function for the policy analysis task
    
    Args:
        self: Task instance (added with bind=True)
        website_url: URL of the website to analyze
        scrape_request_ref_id: Reference ID for the scrape request
        parsed_urls_data: Data containing parsed URLs
        org_id: Organization ID
        
    Returns:
        Results from the policy analysis task
    """
    try:
        from app.tasks.celery_tasks import process_policy_analysis
        return process_policy_analysis(website_url, scrape_request_ref_id, parsed_urls_data, org_id)
    except Exception as exc:
        self.retry(exc=exc, countdown=120 * (2 ** (self.request.retries)))  # Exponential backoff

# Import the test task
from app.tasks.test_task import test_task 